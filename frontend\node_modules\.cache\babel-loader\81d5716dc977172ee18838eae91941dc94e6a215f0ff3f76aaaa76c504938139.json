{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\RaceDistribution.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Progress, Statistic } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, TeamOutlined, UserOutlined, EnvironmentOutlined, CrownOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst RaceDistribution = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [races, setRaces] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingRace, setEditingRace] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockRaces = [{\n    id: 1,\n    name: '人族',\n    type: 'humanoid',\n    population: 1000000,\n    territory: '中原大陆',\n    dominantRegions: ['天元城', '青云山脉', '东海之滨'],\n    characteristics: '适应性强，修炼天赋中等，善于团结协作',\n    culture: '重视血缘关系，崇尚修仙成道',\n    powerLevel: 3,\n    influence: 4,\n    relations: {\n      allies: ['精灵族'],\n      enemies: ['魔族'],\n      neutral: ['妖族', '龙族']\n    },\n    status: 'thriving'\n  }, {\n    id: 2,\n    name: '精灵族',\n    type: 'magical',\n    population: 500000,\n    territory: '翡翠森林',\n    dominantRegions: ['生命之树', '月光湖', '星辰谷'],\n    characteristics: '寿命悠长，魔法天赋极高，与自然和谐共处',\n    culture: '崇尚自然，追求魔法的极致',\n    powerLevel: 4,\n    influence: 3,\n    relations: {\n      allies: ['人族'],\n      enemies: ['魔族'],\n      neutral: ['龙族']\n    },\n    status: 'stable'\n  }];\n  useEffect(() => {\n    loadRaces();\n  }, [projectId]);\n  const loadRaces = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setRaces(mockRaces);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载种族分布失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingRace(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = race => {\n    var _race$dominantRegions, _race$relations, _race$relations$allie, _race$relations2, _race$relations2$enem, _race$relations3, _race$relations3$neut;\n    setEditingRace(race);\n    form.setFieldsValue({\n      ...race,\n      dominantRegions: (_race$dominantRegions = race.dominantRegions) === null || _race$dominantRegions === void 0 ? void 0 : _race$dominantRegions.join(', '),\n      allies: (_race$relations = race.relations) === null || _race$relations === void 0 ? void 0 : (_race$relations$allie = _race$relations.allies) === null || _race$relations$allie === void 0 ? void 0 : _race$relations$allie.join(', '),\n      enemies: (_race$relations2 = race.relations) === null || _race$relations2 === void 0 ? void 0 : (_race$relations2$enem = _race$relations2.enemies) === null || _race$relations2$enem === void 0 ? void 0 : _race$relations2$enem.join(', '),\n      neutral: (_race$relations3 = race.relations) === null || _race$relations3 === void 0 ? void 0 : (_race$relations3$neut = _race$relations3.neutral) === null || _race$relations3$neut === void 0 ? void 0 : _race$relations3$neut.join(', ')\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 模拟API调用\n      setRaces(races.filter(r => r.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$dominantRegio, _values$allies, _values$enemies, _values$neutral;\n      const processedValues = {\n        ...values,\n        dominantRegions: (_values$dominantRegio = values.dominantRegions) === null || _values$dominantRegio === void 0 ? void 0 : _values$dominantRegio.split(',').map(s => s.trim()).filter(s => s),\n        relations: {\n          allies: ((_values$allies = values.allies) === null || _values$allies === void 0 ? void 0 : _values$allies.split(',').map(s => s.trim()).filter(s => s)) || [],\n          enemies: ((_values$enemies = values.enemies) === null || _values$enemies === void 0 ? void 0 : _values$enemies.split(',').map(s => s.trim()).filter(s => s)) || [],\n          neutral: ((_values$neutral = values.neutral) === null || _values$neutral === void 0 ? void 0 : _values$neutral.split(',').map(s => s.trim()).filter(s => s)) || []\n        }\n      };\n      if (editingRace) {\n        // 更新\n        setRaces(races.map(r => r.id === editingRace.id ? {\n          ...r,\n          ...processedValues\n        } : r));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newRace = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setRaces([...races, newRace]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      humanoid: 'blue',\n      magical: 'purple',\n      beast: 'orange',\n      elemental: 'cyan',\n      undead: 'red',\n      divine: 'gold'\n    };\n    return colors[type] || 'default';\n  };\n  const getStatusColor = status => {\n    const colors = {\n      thriving: 'green',\n      stable: 'blue',\n      declining: 'orange',\n      endangered: 'red',\n      extinct: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n  const formatPopulation = population => {\n    if (population >= 1000000) {\n      return `${(population / 1000000).toFixed(1)}M`;\n    } else if (population >= 1000) {\n      return `${(population / 1000).toFixed(1)}K`;\n    }\n    return population.toString();\n  };\n  const columns = [{\n    title: '种族名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'humanoid' ? '类人' : record.type === 'magical' ? '魔法' : record.type === 'beast' ? '兽族' : record.type === 'elemental' ? '元素' : record.type === 'undead' ? '不死' : '神族'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '人口',\n    dataIndex: 'population',\n    key: 'population',\n    render: population => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: formatPopulation(population)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.population - b.population\n  }, {\n    title: '主要领土',\n    dataIndex: 'territory',\n    key: 'territory',\n    render: text => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '实力等级',\n    dataIndex: 'powerLevel',\n    key: 'powerLevel',\n    render: level => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: level * 20,\n      size: \"small\",\n      format: () => `${level}/5`,\n      strokeColor: level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : '#52c41a'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.powerLevel - b.powerLevel\n  }, {\n    title: '影响力',\n    dataIndex: 'influence',\n    key: 'influence',\n    render: influence => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: influence * 20,\n      size: \"small\",\n      format: () => `${influence}/5`,\n      strokeColor: influence >= 4 ? '#722ed1' : influence >= 3 ? '#1890ff' : '#52c41a'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.influence - b.influence\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status === 'thriving' ? '繁荣' : status === 'stable' ? '稳定' : status === 'declining' ? '衰落' : status === 'endangered' ? '濒危' : '灭绝'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u79CD\\u65CF\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this)\n  }];\n  const totalPopulation = races.reduce((sum, race) => sum + race.population, 0);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), \" \\u79CD\\u65CF\\u5206\\u5E03\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u79CD\\u65CF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u79CD\\u65CF\\u603B\\u6570\",\n            value: races.length,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EBA\\u53E3\",\n            value: totalPopulation,\n            formatter: value => formatPopulation(value),\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7E41\\u8363\\u79CD\\u65CF\",\n            value: races.filter(r => r.status === 'thriving').length,\n            prefix: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6FD2\\u5371\\u79CD\\u65CF\",\n            value: races.filter(r => r.status === 'endangered').length,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: races,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个种族`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingRace ? '编辑种族' : '添加种族',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'humanoid',\n          powerLevel: 3,\n          influence: 3,\n          status: 'stable'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u79CD\\u65CF\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入种族名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u79CD\\u65CF\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u79CD\\u65CF\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择种族类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"humanoid\",\n                  children: \"\\u7C7B\\u4EBA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"magical\",\n                  children: \"\\u9B54\\u6CD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"beast\",\n                  children: \"\\u517D\\u65CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"elemental\",\n                  children: \"\\u5143\\u7D20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"undead\",\n                  children: \"\\u4E0D\\u6B7B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"divine\",\n                  children: \"\\u795E\\u65CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"population\",\n              label: \"\\u4EBA\\u53E3\\u6570\\u91CF\",\n              rules: [{\n                required: true,\n                message: '请输入人口数量'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EBA\\u53E3\\u6570\\u91CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"territory\",\n              label: \"\\u4E3B\\u8981\\u9886\\u571F\",\n              rules: [{\n                required: true,\n                message: '请输入主要领土'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E3B\\u8981\\u9886\\u571F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"dominantRegions\",\n          label: \"\\u7EDF\\u6CBB\\u533A\\u57DF\",\n          extra: \"\\u591A\\u4E2A\\u533A\\u57DF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4F8B\\u5982\\uFF1A\\u5929\\u5143\\u57CE, \\u9752\\u4E91\\u5C71\\u8109, \\u4E1C\\u6D77\\u4E4B\\u6EE8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"powerLevel\",\n              label: \"\\u5B9E\\u529B\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择实力等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"influence\",\n              label: \"\\u5F71\\u54CD\\u529B\",\n              rules: [{\n                required: true,\n                message: '请选择影响力'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u79CD\\u65CF\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择种族状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"thriving\",\n                  children: \"\\u7E41\\u8363\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"stable\",\n                  children: \"\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"declining\",\n                  children: \"\\u8870\\u843D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"endangered\",\n                  children: \"\\u6FD2\\u5371\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"extinct\",\n                  children: \"\\u706D\\u7EDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"characteristics\",\n          label: \"\\u79CD\\u65CF\\u7279\\u5F81\",\n          rules: [{\n            required: true,\n            message: '请输入种族特征'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u79CD\\u65CF\\u7684\\u751F\\u7406\\u7279\\u5F81\\u3001\\u80FD\\u529B\\u7279\\u70B9\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"culture\",\n          label: \"\\u6587\\u5316\\u80CC\\u666F\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u79CD\\u65CF\\u7684\\u6587\\u5316\\u3001\\u4FE1\\u4EF0\\u3001\\u4F20\\u7EDF\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"allies\",\n              label: \"\\u76DF\\u53CB\\u79CD\\u65CF\",\n              extra: \"\\u591A\\u4E2A\\u79CD\\u65CF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u4F8B\\u5982\\uFF1A\\u7CBE\\u7075\\u65CF, \\u77EE\\u4EBA\\u65CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"enemies\",\n              label: \"\\u654C\\u5BF9\\u79CD\\u65CF\",\n              extra: \"\\u591A\\u4E2A\\u79CD\\u65CF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u4F8B\\u5982\\uFF1A\\u9B54\\u65CF, \\u517D\\u4EBA\\u65CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"neutral\",\n              label: \"\\u4E2D\\u7ACB\\u79CD\\u65CF\",\n              extra: \"\\u591A\\u4E2A\\u79CD\\u65CF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u4F8B\\u5982\\uFF1A\\u9F99\\u65CF, \\u5996\\u65CF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n};\n_s(RaceDistribution, \"MMZNWHY9d7DHV6Z3AYdHujMDc28=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = RaceDistribution;\nexport default RaceDistribution;\nvar _c;\n$RefreshReg$(_c, \"RaceDistribution\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Progress", "Statistic", "PlusOutlined", "EditOutlined", "DeleteOutlined", "TeamOutlined", "UserOutlined", "EnvironmentOutlined", "CrownOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "RaceDistribution", "_s", "id", "projectId", "races", "setRaces", "loading", "setLoading", "modalVisible", "setModalVisible", "editingRace", "setEditingRace", "form", "useForm", "mockRaces", "name", "type", "population", "territory", "dominantRegions", "characteristics", "culture", "powerLevel", "influence", "relations", "allies", "enemies", "neutral", "status", "loadRaces", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "race", "_race$dominantRegions", "_race$relations", "_race$relations$allie", "_race$relations2", "_race$relations2$enem", "_race$relations3", "_race$relations3$neut", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "handleDelete", "filter", "r", "success", "handleSubmit", "values", "_values$dominantRegio", "_values$allies", "_values$enemies", "_values$neutral", "processedValues", "split", "map", "s", "trim", "newRace", "Date", "now", "getTypeColor", "colors", "humanoid", "magical", "beast", "elemental", "undead", "divine", "getStatusColor", "thriving", "stable", "declining", "endangered", "extinct", "formatPopulation", "toFixed", "toString", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "sorter", "a", "b", "level", "percent", "size", "format", "strokeColor", "_", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "totalPopulation", "reduce", "sum", "className", "gutter", "style", "marginBottom", "span", "value", "length", "prefix", "formatter", "valueStyle", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "extra", "max", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/RaceDistribution.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Progress,\n  Statistic\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  TeamOutlined,\n  UserOutlined,\n  EnvironmentOutlined,\n  CrownOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst RaceDistribution = () => {\n  const { id: projectId } = useParams();\n  const [races, setRaces] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingRace, setEditingRace] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockRaces = [\n    {\n      id: 1,\n      name: '人族',\n      type: 'humanoid',\n      population: 1000000,\n      territory: '中原大陆',\n      dominantRegions: ['天元城', '青云山脉', '东海之滨'],\n      characteristics: '适应性强，修炼天赋中等，善于团结协作',\n      culture: '重视血缘关系，崇尚修仙成道',\n      powerLevel: 3,\n      influence: 4,\n      relations: {\n        allies: ['精灵族'],\n        enemies: ['魔族'],\n        neutral: ['妖族', '龙族']\n      },\n      status: 'thriving'\n    },\n    {\n      id: 2,\n      name: '精灵族',\n      type: 'magical',\n      population: 500000,\n      territory: '翡翠森林',\n      dominantRegions: ['生命之树', '月光湖', '星辰谷'],\n      characteristics: '寿命悠长，魔法天赋极高，与自然和谐共处',\n      culture: '崇尚自然，追求魔法的极致',\n      powerLevel: 4,\n      influence: 3,\n      relations: {\n        allies: ['人族'],\n        enemies: ['魔族'],\n        neutral: ['龙族']\n      },\n      status: 'stable'\n    }\n  ];\n\n  useEffect(() => {\n    loadRaces();\n  }, [projectId]);\n\n  const loadRaces = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setRaces(mockRaces);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载种族分布失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingRace(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (race) => {\n    setEditingRace(race);\n    form.setFieldsValue({\n      ...race,\n      dominantRegions: race.dominantRegions?.join(', '),\n      allies: race.relations?.allies?.join(', '),\n      enemies: race.relations?.enemies?.join(', '),\n      neutral: race.relations?.neutral?.join(', ')\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 模拟API调用\n      setRaces(races.filter(r => r.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      const processedValues = {\n        ...values,\n        dominantRegions: values.dominantRegions?.split(',').map(s => s.trim()).filter(s => s),\n        relations: {\n          allies: values.allies?.split(',').map(s => s.trim()).filter(s => s) || [],\n          enemies: values.enemies?.split(',').map(s => s.trim()).filter(s => s) || [],\n          neutral: values.neutral?.split(',').map(s => s.trim()).filter(s => s) || []\n        }\n      };\n\n      if (editingRace) {\n        // 更新\n        setRaces(races.map(r =>\n          r.id === editingRace.id ? { ...r, ...processedValues } : r\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newRace = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setRaces([...races, newRace]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      humanoid: 'blue',\n      magical: 'purple',\n      beast: 'orange',\n      elemental: 'cyan',\n      undead: 'red',\n      divine: 'gold'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      thriving: 'green',\n      stable: 'blue',\n      declining: 'orange',\n      endangered: 'red',\n      extinct: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n\n  const formatPopulation = (population) => {\n    if (population >= 1000000) {\n      return `${(population / 1000000).toFixed(1)}M`;\n    } else if (population >= 1000) {\n      return `${(population / 1000).toFixed(1)}K`;\n    }\n    return population.toString();\n  };\n\n  const columns = [\n    {\n      title: '种族名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'humanoid' ? '类人' :\n             record.type === 'magical' ? '魔法' :\n             record.type === 'beast' ? '兽族' :\n             record.type === 'elemental' ? '元素' :\n             record.type === 'undead' ? '不死' : '神族'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '人口',\n      dataIndex: 'population',\n      key: 'population',\n      render: (population) => (\n        <Space>\n          <UserOutlined />\n          <Text>{formatPopulation(population)}</Text>\n        </Space>\n      ),\n      sorter: (a, b) => a.population - b.population\n    },\n    {\n      title: '主要领土',\n      dataIndex: 'territory',\n      key: 'territory',\n      render: (text) => (\n        <Space>\n          <EnvironmentOutlined />\n          <Text>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '实力等级',\n      dataIndex: 'powerLevel',\n      key: 'powerLevel',\n      render: (level) => (\n        <Progress\n          percent={level * 20}\n          size=\"small\"\n          format={() => `${level}/5`}\n          strokeColor={level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : '#52c41a'}\n        />\n      ),\n      sorter: (a, b) => a.powerLevel - b.powerLevel\n    },\n    {\n      title: '影响力',\n      dataIndex: 'influence',\n      key: 'influence',\n      render: (influence) => (\n        <Progress\n          percent={influence * 20}\n          size=\"small\"\n          format={() => `${influence}/5`}\n          strokeColor={influence >= 4 ? '#722ed1' : influence >= 3 ? '#1890ff' : '#52c41a'}\n        />\n      ),\n      sorter: (a, b) => a.influence - b.influence\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'thriving' ? '繁荣' :\n           status === 'stable' ? '稳定' :\n           status === 'declining' ? '衰落' :\n           status === 'endangered' ? '濒危' : '灭绝'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个种族吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  const totalPopulation = races.reduce((sum, race) => sum + race.population, 0);\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <TeamOutlined /> 种族分布管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加种族\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"种族总数\"\n              value={races.length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"总人口\"\n              value={totalPopulation}\n              formatter={(value) => formatPopulation(value)}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"繁荣种族\"\n              value={races.filter(r => r.status === 'thriving').length}\n              prefix={<CrownOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"濒危种族\"\n              value={races.filter(r => r.status === 'endangered').length}\n              prefix={<TeamOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={races}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个种族`\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingRace ? '编辑种族' : '添加种族'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'humanoid',\n            powerLevel: 3,\n            influence: 3,\n            status: 'stable'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"种族名称\"\n                rules={[{ required: true, message: '请输入种族名称' }]}\n              >\n                <Input placeholder=\"请输入种族名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"种族类型\"\n                rules={[{ required: true, message: '请选择种族类型' }]}\n              >\n                <Select>\n                  <Option value=\"humanoid\">类人</Option>\n                  <Option value=\"magical\">魔法</Option>\n                  <Option value=\"beast\">兽族</Option>\n                  <Option value=\"elemental\">元素</Option>\n                  <Option value=\"undead\">不死</Option>\n                  <Option value=\"divine\">神族</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"population\"\n                label=\"人口数量\"\n                rules={[{ required: true, message: '请输入人口数量' }]}\n              >\n                <InputNumber\n                  min={0}\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入人口数量\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"territory\"\n                label=\"主要领土\"\n                rules={[{ required: true, message: '请输入主要领土' }]}\n              >\n                <Input placeholder=\"请输入主要领土\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"dominantRegions\"\n            label=\"统治区域\"\n            extra=\"多个区域请用逗号分隔\"\n          >\n            <Input placeholder=\"例如：天元城, 青云山脉, 东海之滨\" />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"powerLevel\"\n                label=\"实力等级\"\n                rules={[{ required: true, message: '请选择实力等级' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"influence\"\n                label=\"影响力\"\n                rules={[{ required: true, message: '请选择影响力' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"种族状态\"\n                rules={[{ required: true, message: '请选择种族状态' }]}\n              >\n                <Select>\n                  <Option value=\"thriving\">繁荣</Option>\n                  <Option value=\"stable\">稳定</Option>\n                  <Option value=\"declining\">衰落</Option>\n                  <Option value=\"endangered\">濒危</Option>\n                  <Option value=\"extinct\">灭绝</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"characteristics\"\n            label=\"种族特征\"\n            rules={[{ required: true, message: '请输入种族特征' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请描述种族的生理特征、能力特点等\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"culture\"\n            label=\"文化背景\"\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"请描述种族的文化、信仰、传统等\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"allies\"\n                label=\"盟友种族\"\n                extra=\"多个种族请用逗号分隔\"\n              >\n                <Input placeholder=\"例如：精灵族, 矮人族\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"enemies\"\n                label=\"敌对种族\"\n                extra=\"多个种族请用逗号分隔\"\n              >\n                <Input placeholder=\"例如：魔族, 兽人族\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"neutral\"\n                label=\"中立种族\"\n                extra=\"多个种族请用逗号分隔\"\n              >\n                <Input placeholder=\"例如：龙族, 妖族\" />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RaceDistribution;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,SAAS,QACJ,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGnB,UAAU;AAClC,MAAM;EAAEoB;AAAS,CAAC,GAAGxB,KAAK;AAC1B,MAAM;EAAEyB;AAAO,CAAC,GAAGxB,MAAM;AAEzB,MAAMyB,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGnC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8C,IAAI,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,SAAS,GAAG,CAChB;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,UAAU;IAChBC,UAAU,EAAE,OAAO;IACnBC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;IACxCC,eAAe,EAAE,oBAAoB;IACrCC,OAAO,EAAE,eAAe;IACxBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;MACTC,MAAM,EAAE,CAAC,KAAK,CAAC;MACfC,OAAO,EAAE,CAAC,IAAI,CAAC;MACfC,OAAO,EAAE,CAAC,IAAI,EAAE,IAAI;IACtB,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,EACD;IACE1B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,SAAS;IACfC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,MAAM;IACjBC,eAAe,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IACvCC,eAAe,EAAE,qBAAqB;IACtCC,OAAO,EAAE,cAAc;IACvBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;MACTC,MAAM,EAAE,CAAC,IAAI,CAAC;MACdC,OAAO,EAAE,CAAC,IAAI,CAAC;MACfC,OAAO,EAAE,CAAC,IAAI;IAChB,CAAC;IACDC,MAAM,EAAE;EACV,CAAC,CACF;EAED7D,SAAS,CAAC,MAAM;IACd8D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EAEf,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAuB,UAAU,CAAC,MAAM;QACfzB,QAAQ,CAACS,SAAS,CAAC;QACnBP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,UAAU,CAAC;MACzBxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IACtBrB,cAAc,CAAC,IAAI,CAAC;IACpBC,IAAI,CAACqB,WAAW,CAAC,CAAC;IAClBxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyB,UAAU,GAAIC,IAAI,IAAK;IAAA,IAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IAC3B/B,cAAc,CAACwB,IAAI,CAAC;IACpBvB,IAAI,CAAC+B,cAAc,CAAC;MAClB,GAAGR,IAAI;MACPhB,eAAe,GAAAiB,qBAAA,GAAED,IAAI,CAAChB,eAAe,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBQ,IAAI,CAAC,IAAI,CAAC;MACjDnB,MAAM,GAAAY,eAAA,GAAEF,IAAI,CAACX,SAAS,cAAAa,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBZ,MAAM,cAAAa,qBAAA,uBAAtBA,qBAAA,CAAwBM,IAAI,CAAC,IAAI,CAAC;MAC1ClB,OAAO,GAAAa,gBAAA,GAAEJ,IAAI,CAACX,SAAS,cAAAe,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBb,OAAO,cAAAc,qBAAA,uBAAvBA,qBAAA,CAAyBI,IAAI,CAAC,IAAI,CAAC;MAC5CjB,OAAO,GAAAc,gBAAA,GAAEN,IAAI,CAACX,SAAS,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBd,OAAO,cAAAe,qBAAA,uBAAvBA,qBAAA,CAAyBE,IAAI,CAAC,IAAI;IAC7C,CAAC,CAAC;IACFnC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAO3C,EAAE,IAAK;IACjC,IAAI;MACF;MACAG,QAAQ,CAACD,KAAK,CAAC0C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,EAAE,KAAKA,EAAE,CAAC,CAAC;MACxCnB,OAAO,CAACiE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MAAA,IAAAC,qBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA;MACF,MAAMC,eAAe,GAAG;QACtB,GAAGL,MAAM;QACT/B,eAAe,GAAAgC,qBAAA,GAAED,MAAM,CAAC/B,eAAe,cAAAgC,qBAAA,uBAAtBA,qBAAA,CAAwBK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAACY,CAAC,IAAIA,CAAC,CAAC;QACrFlC,SAAS,EAAE;UACTC,MAAM,EAAE,EAAA2B,cAAA,GAAAF,MAAM,CAACzB,MAAM,cAAA2B,cAAA,uBAAbA,cAAA,CAAeI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAACY,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;UACzEhC,OAAO,EAAE,EAAA2B,eAAA,GAAAH,MAAM,CAACxB,OAAO,cAAA2B,eAAA,uBAAdA,eAAA,CAAgBG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAACY,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;UAC3E/B,OAAO,EAAE,EAAA2B,eAAA,GAAAJ,MAAM,CAACvB,OAAO,cAAA2B,eAAA,uBAAdA,eAAA,CAAgBE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAACY,CAAC,IAAIA,CAAC,CAAC,KAAI;QAC3E;MACF,CAAC;MAED,IAAIhD,WAAW,EAAE;QACf;QACAL,QAAQ,CAACD,KAAK,CAACqD,GAAG,CAACV,CAAC,IAClBA,CAAC,CAAC7C,EAAE,KAAKQ,WAAW,CAACR,EAAE,GAAG;UAAE,GAAG6C,CAAC;UAAE,GAAGQ;QAAgB,CAAC,GAAGR,CAC3D,CAAC,CAAC;QACFhE,OAAO,CAACiE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMY,OAAO,GAAG;UACd1D,EAAE,EAAE2D,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGP;QACL,CAAC;QACDlD,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAEwD,OAAO,CAAC,CAAC;QAC7B7E,OAAO,CAACiE,OAAO,CAAC,MAAM,CAAC;MACzB;MACAvC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMgC,YAAY,GAAI/C,IAAI,IAAK;IAC7B,MAAMgD,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,MAAM;MACjBC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,MAAM,CAAChD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAMuD,cAAc,GAAI3C,MAAM,IAAK;IACjC,MAAMoC,MAAM,GAAG;MACbQ,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOZ,MAAM,CAACpC,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMiD,gBAAgB,GAAI5D,UAAU,IAAK;IACvC,IAAIA,UAAU,IAAI,OAAO,EAAE;MACzB,OAAO,GAAG,CAACA,UAAU,GAAG,OAAO,EAAE6D,OAAO,CAAC,CAAC,CAAC,GAAG;IAChD,CAAC,MAAM,IAAI7D,UAAU,IAAI,IAAI,EAAE;MAC7B,OAAO,GAAG,CAACA,UAAU,GAAG,IAAI,EAAE6D,OAAO,CAAC,CAAC,CAAC,GAAG;IAC7C;IACA,OAAO7D,UAAU,CAAC8D,QAAQ,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB3F,OAAA,CAAClB,KAAK;MAAA8G,QAAA,gBACJ5F,OAAA,CAACE,IAAI;QAAC2F,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BjG,OAAA,CAACd,GAAG;QAACgH,KAAK,EAAE9B,YAAY,CAACuB,MAAM,CAACtE,IAAI,CAAE;QAAAuE,QAAA,EACnCD,MAAM,CAACtE,IAAI,KAAK,UAAU,GAAG,IAAI,GACjCsE,MAAM,CAACtE,IAAI,KAAK,SAAS,GAAG,IAAI,GAChCsE,MAAM,CAACtE,IAAI,KAAK,OAAO,GAAG,IAAI,GAC9BsE,MAAM,CAACtE,IAAI,KAAK,WAAW,GAAG,IAAI,GAClCsE,MAAM,CAACtE,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;MAAI;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGnE,UAAU,iBACjBtB,OAAA,CAAClB,KAAK;MAAA8G,QAAA,gBACJ5F,OAAA,CAACJ,YAAY;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBjG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEV,gBAAgB,CAAC5D,UAAU;MAAC;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACR;IACDE,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9E,UAAU,GAAG+E,CAAC,CAAC/E;EACrC,CAAC,EACD;IACEgE,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGC,IAAI,iBACX1F,OAAA,CAAClB,KAAK;MAAA8G,QAAA,gBACJ5F,OAAA,CAACH,mBAAmB;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBjG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGa,KAAK,iBACZtG,OAAA,CAACV,QAAQ;MACPiH,OAAO,EAAED,KAAK,GAAG,EAAG;MACpBE,IAAI,EAAC,OAAO;MACZC,MAAM,EAAEA,CAAA,KAAM,GAAGH,KAAK,IAAK;MAC3BI,WAAW,EAAEJ,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG;IAAU;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACF;IACDE,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzE,UAAU,GAAG0E,CAAC,CAAC1E;EACrC,CAAC,EACD;IACE2D,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAG7D,SAAS,iBAChB5B,OAAA,CAACV,QAAQ;MACPiH,OAAO,EAAE3E,SAAS,GAAG,EAAG;MACxB4E,IAAI,EAAC,OAAO;MACZC,MAAM,EAAEA,CAAA,KAAM,GAAG7E,SAAS,IAAK;MAC/B8E,WAAW,EAAE9E,SAAS,IAAI,CAAC,GAAG,SAAS,GAAGA,SAAS,IAAI,CAAC,GAAG,SAAS,GAAG;IAAU;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CACF;IACDE,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,SAAS,GAAGyE,CAAC,CAACzE;EACpC,CAAC,EACD;IACE0D,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGxD,MAAM,iBACbjC,OAAA,CAACd,GAAG;MAACgH,KAAK,EAAEtB,cAAc,CAAC3C,MAAM,CAAE;MAAA2D,QAAA,EAChC3D,MAAM,KAAK,UAAU,GAAG,IAAI,GAC5BA,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,WAAW,GAAG,IAAI,GAC7BA,MAAM,KAAK,YAAY,GAAG,IAAI,GAAG;IAAI;MAAA6D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACkB,CAAC,EAAEhB,MAAM,kBAChB3F,OAAA,CAAClB,KAAK;MAAA8G,QAAA,gBACJ5F,OAAA,CAACX,OAAO;QAACiG,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB5F,OAAA,CAACxB,MAAM;UACL6C,IAAI,EAAC,MAAM;UACXuF,IAAI,eAAE5G,OAAA,CAACP,YAAY;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBY,OAAO,EAAEA,CAAA,KAAMtE,UAAU,CAACoD,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjG,OAAA,CAACb,UAAU;QACTmG,KAAK,EAAC,8DAAY;QAClBwB,SAAS,EAAEA,CAAA,KAAM5D,YAAY,CAACyC,MAAM,CAACpF,EAAE,CAAE;QACzCwG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEf5F,OAAA,CAACX,OAAO;UAACiG,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjB5F,OAAA,CAACxB,MAAM;YACL6C,IAAI,EAAC,MAAM;YACX4F,MAAM;YACNL,IAAI,eAAE5G,OAAA,CAACN,cAAc;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMiB,eAAe,GAAGzG,KAAK,CAAC0G,MAAM,CAAC,CAACC,GAAG,EAAE5E,IAAI,KAAK4E,GAAG,GAAG5E,IAAI,CAAClB,UAAU,EAAE,CAAC,CAAC;EAE7E,oBACEtB,OAAA;IAAKqH,SAAS,EAAC,SAAS;IAAAzB,QAAA,gBACtB5F,OAAA;MAAKqH,SAAS,EAAC,aAAa;MAAAzB,QAAA,gBAC1B5F,OAAA,CAACC,KAAK;QAACqG,KAAK,EAAE,CAAE;QAACe,SAAS,EAAC,YAAY;QAAAzB,QAAA,gBACrC5F,OAAA,CAACL,YAAY;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjG,OAAA,CAACxB,MAAM;QACL6C,IAAI,EAAC,SAAS;QACduF,IAAI,eAAE5G,OAAA,CAACR,YAAY;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAExE,SAAU;QAAAuD,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjG,OAAA,CAAChB,GAAG;MAACsI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAA5B,QAAA,gBACjD5F,OAAA,CAACf,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACX5F,OAAA,CAAC1B,IAAI;UAACkI,IAAI,EAAC,OAAO;UAAAZ,QAAA,eAChB5F,OAAA,CAACT,SAAS;YACR+F,KAAK,EAAC,0BAAM;YACZoC,KAAK,EAAEjH,KAAK,CAACkH,MAAO;YACpBC,MAAM,eAAE5H,OAAA,CAACL,YAAY;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAACf,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACX5F,OAAA,CAAC1B,IAAI;UAACkI,IAAI,EAAC,OAAO;UAAAZ,QAAA,eAChB5F,OAAA,CAACT,SAAS;YACR+F,KAAK,EAAC,oBAAK;YACXoC,KAAK,EAAER,eAAgB;YACvBW,SAAS,EAAGH,KAAK,IAAKxC,gBAAgB,CAACwC,KAAK,CAAE;YAC9CE,MAAM,eAAE5H,OAAA,CAACJ,YAAY;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAACf,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACX5F,OAAA,CAAC1B,IAAI;UAACkI,IAAI,EAAC,OAAO;UAAAZ,QAAA,eAChB5F,OAAA,CAACT,SAAS;YACR+F,KAAK,EAAC,0BAAM;YACZoC,KAAK,EAAEjH,KAAK,CAAC0C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnB,MAAM,KAAK,UAAU,CAAC,CAAC0F,MAAO;YACzDC,MAAM,eAAE5H,OAAA,CAACF,aAAa;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1B6B,UAAU,EAAE;cAAE5B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAACf,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACX5F,OAAA,CAAC1B,IAAI;UAACkI,IAAI,EAAC,OAAO;UAAAZ,QAAA,eAChB5F,OAAA,CAACT,SAAS;YACR+F,KAAK,EAAC,0BAAM;YACZoC,KAAK,EAAEjH,KAAK,CAAC0C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnB,MAAM,KAAK,YAAY,CAAC,CAAC0F,MAAO;YAC3DC,MAAM,eAAE5H,OAAA,CAACL,YAAY;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB6B,UAAU,EAAE;cAAE5B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjG,OAAA,CAAC1B,IAAI;MAAAsH,QAAA,eACH5F,OAAA,CAACzB,KAAK;QACJ8G,OAAO,EAAEA,OAAQ;QACjB0C,UAAU,EAAEtH,KAAM;QAClBuH,MAAM,EAAC,IAAI;QACXrH,OAAO,EAAEA,OAAQ;QACjBsH,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPjG,OAAA,CAACvB,KAAK;MACJ6G,KAAK,EAAEvE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCwH,IAAI,EAAE1H,YAAa;MACnB2H,QAAQ,EAAEA,CAAA,KAAM1H,eAAe,CAAC,KAAK,CAAE;MACvC2H,IAAI,EAAEA,CAAA,KAAMxH,IAAI,CAACyH,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAEhI,OAAQ;MACxBiI,KAAK,EAAE,GAAI;MAAAhD,QAAA,eAEX5F,OAAA,CAACtB,IAAI;QACHuC,IAAI,EAAEA,IAAK;QACX4H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAExF,YAAa;QACvByF,aAAa,EAAE;UACb1H,IAAI,EAAE,UAAU;UAChBM,UAAU,EAAE,CAAC;UACbC,SAAS,EAAE,CAAC;UACZK,MAAM,EAAE;QACV,CAAE;QAAA2D,QAAA,gBAEF5F,OAAA,CAAChB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAA1B,QAAA,gBACd5F,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA7B,QAAA,eACZ5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,MAAM;cACX6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACrB,KAAK;gBAACyK,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA7B,QAAA,eACZ5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,MAAM;cACX6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACpB,MAAM;gBAAAgH,QAAA,gBACL5F,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,UAAU;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,SAAS;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,OAAO;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,WAAW;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAAChB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAA1B,QAAA,gBACd5F,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA7B,QAAA,eACZ5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,YAAY;cACjB6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACnB,WAAW;gBACVwK,GAAG,EAAE,CAAE;gBACP9B,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO,CAAE;gBACzBQ,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA7B,QAAA,eACZ5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,WAAW;cAChB6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACrB,KAAK;gBAACyK,WAAW,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACtB,IAAI,CAACsK,IAAI;UACR5H,IAAI,EAAC,iBAAiB;UACtB6H,KAAK,EAAC,0BAAM;UACZK,KAAK,EAAC,8DAAY;UAAA1D,QAAA,eAElB5F,OAAA,CAACrB,KAAK;YAACyK,WAAW,EAAC;UAAoB;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEZjG,OAAA,CAAChB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAA1B,QAAA,gBACd5F,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,YAAY;cACjB6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACnB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACE,GAAG,EAAE,CAAE;gBAAChC,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,WAAW;cAChB6H,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAwG,QAAA,eAE/C5F,OAAA,CAACnB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACE,GAAG,EAAE,CAAE;gBAAChC,KAAK,EAAE;kBAAEqB,KAAK,EAAE;gBAAO;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,QAAQ;cACb6H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwG,QAAA,eAEhD5F,OAAA,CAACpB,MAAM;gBAAAgH,QAAA,gBACL5F,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,UAAU;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,WAAW;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,YAAY;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCjG,OAAA,CAACI,MAAM;kBAACsH,KAAK,EAAC,SAAS;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACtB,IAAI,CAACsK,IAAI;UACR5H,IAAI,EAAC,iBAAiB;UACtB6H,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/J,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwG,QAAA,eAEhD5F,OAAA,CAACG,QAAQ;YACPqJ,IAAI,EAAE,CAAE;YACRJ,WAAW,EAAC;UAAkB;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZjG,OAAA,CAACtB,IAAI,CAACsK,IAAI;UACR5H,IAAI,EAAC,SAAS;UACd6H,KAAK,EAAC,0BAAM;UAAArD,QAAA,eAEZ5F,OAAA,CAACG,QAAQ;YACPqJ,IAAI,EAAE,CAAE;YACRJ,WAAW,EAAC;UAAiB;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZjG,OAAA,CAAChB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAA1B,QAAA,gBACd5F,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,QAAQ;cACb6H,KAAK,EAAC,0BAAM;cACZK,KAAK,EAAC,8DAAY;cAAA1D,QAAA,eAElB5F,OAAA,CAACrB,KAAK;gBAACyK,WAAW,EAAC;cAAa;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,SAAS;cACd6H,KAAK,EAAC,0BAAM;cACZK,KAAK,EAAC,8DAAY;cAAA1D,QAAA,eAElB5F,OAAA,CAACrB,KAAK;gBAACyK,WAAW,EAAC;cAAY;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAACf,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA7B,QAAA,eACX5F,OAAA,CAACtB,IAAI,CAACsK,IAAI;cACR5H,IAAI,EAAC,SAAS;cACd6H,KAAK,EAAC,0BAAM;cACZK,KAAK,EAAC,8DAAY;cAAA1D,QAAA,eAElB5F,OAAA,CAACrB,KAAK;gBAACyK,WAAW,EAAC;cAAW;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3F,EAAA,CApgBID,gBAAgB;EAAA,QACMhC,SAAS,EAKpBK,IAAI,CAACwC,OAAO;AAAA;AAAAuI,EAAA,GANvBpJ,gBAAgB;AAsgBtB,eAAeA,gBAAgB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}