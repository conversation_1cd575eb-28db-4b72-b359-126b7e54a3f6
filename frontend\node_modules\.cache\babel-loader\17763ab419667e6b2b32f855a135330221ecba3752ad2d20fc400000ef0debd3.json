{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\WorldSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Tooltip, Row, Col, Statistic, Descriptions, Tabs, Collapse } from 'antd';\nimport { PlusOutlined, GlobalOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, EnvironmentOutlined, HistoryOutlined, BankOutlined, TeamOutlined, BookOutlined, DollarOutlined, ShopOutlined, UsergroupAddOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Panel\n} = Collapse;\nconst WorldSettings = () => {\n  _s();\n  const [settings, setSettings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingSetting, setEditingSetting] = useState(null);\n  const [viewingSetting, setViewingSetting] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟世界设定数据\n  const mockSettings = [{\n    id: 1,\n    name: '修仙世界基础设定',\n    category: 'world_basic',\n    description: '修仙世界的基本世界观和规则',\n    content: {\n      worldName: '九州大陆',\n      geography: '九州大陆分为九个州，每州都有不同的地理环境和修炼资源',\n      history: '上古时期，仙魔大战，仙界封印，修仙者只能在凡间修炼',\n      politics: '各大宗门割据一方，皇朝统治凡人，修仙者超然物外',\n      economy: '以灵石为主要货币，凡人使用金银铜钱',\n      culture: '尊师重道，强者为尊，追求长生不老'\n    },\n    status: 'active',\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-16'\n  }, {\n    id: 2,\n    name: '修炼体系',\n    category: 'cultivation',\n    description: '详细的修炼境界和体系',\n    content: {\n      realms: ['练气期', '筑基期', '金丹期', '元婴期', '化神期', '合体期', '大乘期', '渡劫期'],\n      methods: '吸收天地灵气，炼化为真元，淬炼肉身和神魂',\n      resources: '灵石、丹药、法器、功法、灵草',\n      bottlenecks: '每个大境界都有天劫考验',\n      lifespan: '每提升一个大境界，寿命大幅增加'\n    },\n    status: 'active',\n    createdAt: '2024-01-17',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    name: '宗门势力',\n    category: 'factions',\n    description: '各大宗门和势力的详细设定',\n    content: {\n      majorSects: ['青云宗', '天剑门', '万花谷', '血煞门'],\n      relationships: '正邪对立，内部也有竞争',\n      territories: '各占一方，互不侵犯',\n      resources: '控制灵脉和修炼资源',\n      hierarchy: '宗主、长老、内门弟子、外门弟子'\n    },\n    status: 'draft',\n    createdAt: '2024-01-19',\n    updatedAt: '2024-01-19'\n  }];\n  useEffect(() => {\n    setSettings(mockSettings);\n  }, []);\n\n  // 设定类型配置\n  const categoryConfig = {\n    world_basic: {\n      color: 'blue',\n      text: '世界基础',\n      icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 55\n      }, this)\n    },\n    geography: {\n      color: 'green',\n      text: '地理环境',\n      icon: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 54\n      }, this)\n    },\n    history: {\n      color: 'orange',\n      text: '历史文化',\n      icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 53\n      }, this)\n    },\n    politics: {\n      color: 'red',\n      text: '政治体系',\n      icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 51\n      }, this)\n    },\n    currency: {\n      color: 'gold',\n      text: '货币体系',\n      icon: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 52\n      }, this)\n    },\n    commerce: {\n      color: 'lime',\n      text: '商业体系',\n      icon: /*#__PURE__*/_jsxDEV(ShopOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 52\n      }, this)\n    },\n    races: {\n      color: 'magenta',\n      text: '种族类别',\n      icon: /*#__PURE__*/_jsxDEV(UsergroupAddOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 52\n      }, this)\n    },\n    martial_arts: {\n      color: 'volcano',\n      text: '功法体系',\n      icon: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 59\n      }, this)\n    },\n    cultivation: {\n      color: 'purple',\n      text: '修炼体系',\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 57\n      }, this)\n    },\n    factions: {\n      color: 'cyan',\n      text: '势力组织',\n      icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 52\n      }, this)\n    },\n    economy: {\n      color: 'geekblue',\n      text: '经济制度',\n      icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 55\n      }, this)\n    },\n    other: {\n      color: 'default',\n      text: '其他',\n      icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 50\n      }, this)\n    }\n  };\n\n  // 表格列配置\n  const columns = [{\n    title: '设定名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [categoryConfig[record.category].icon, /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '类型',\n    dataIndex: 'category',\n    key: 'category',\n    render: category => /*#__PURE__*/_jsxDEV(Tag, {\n      color: categoryConfig[category].color,\n      children: categoryConfig[category].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this),\n    filters: Object.keys(categoryConfig).map(key => ({\n      text: categoryConfig[key].text,\n      value: key\n    })),\n    onFilter: (value, record) => record.category === value\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'green' : 'orange',\n      children: status === 'active' ? '已完成' : '草稿'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerate(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u8BBE\\u5B9A\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理新建/编辑设定\n  const handleCreateOrEdit = () => {\n    setEditingSetting(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = setting => {\n    setEditingSetting(setting);\n    form.setFieldsValue({\n      ...setting,\n      ...setting.content\n    });\n    setModalVisible(true);\n  };\n  const handleView = setting => {\n    setViewingSetting(setting);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = setting => {\n    message.info(`AI生成世界设定：${setting.name}`);\n  };\n  const handleDelete = id => {\n    setSettings(settings.filter(s => s.id !== id));\n    message.success('设定删除成功');\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 分离基本信息和内容\n      const {\n        name,\n        category,\n        description,\n        status,\n        ...content\n      } = values;\n      const processedValues = {\n        name,\n        category,\n        description,\n        status,\n        content\n      };\n      if (editingSetting) {\n        // 编辑设定\n        setSettings(settings.map(s => s.id === editingSetting.id ? {\n          ...s,\n          ...processedValues,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : s));\n        message.success('设定更新成功');\n      } else {\n        // 新建设定\n        const newSetting = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setSettings([...settings, newSetting]);\n        message.success('设定创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalSettings = settings.length;\n  const activeSettings = settings.filter(s => s.status === 'active').length;\n  const draftSettings = settings.filter(s => s.status === 'draft').length;\n  const categoryStats = Object.keys(categoryConfig).reduce((acc, key) => {\n    acc[key] = settings.filter(s => s.category === key).length;\n    return acc;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u4E16\\u754C\\u8BBE\\u5B9A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BBE\\u5B9A\\u6570\",\n            value: totalSettings,\n            prefix: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: activeSettings,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8349\\u7A3F\",\n            value: draftSettings,\n            prefix: /*#__PURE__*/_jsxDEV(EditOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4FEE\\u70BC\\u4F53\\u7CFB\",\n            value: categoryStats.cultivation || 0,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u8BBE\\u5B9A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: settings,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个设定`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingSetting ? '编辑世界设定' : '新建世界设定',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 1000,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          category: 'world_basic',\n          status: 'draft'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u8BBE\\u5B9A\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入设定名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5B9A\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u8BBE\\u5B9A\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择设定类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: Object.keys(categoryConfig).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: categoryConfig[key].text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5B9A\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入设定描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u7B80\\u8981\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u8BBE\\u5B9A\\u7684\\u5185\\u5BB9\\u548C\\u4F5C\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"draft\",\n              children: \"\\u8349\\u7A3F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"active\",\n              children: \"\\u5DF2\\u5B8C\\u6210\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          defaultActiveKey: \"basic\",\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u57FA\\u7840\\u4FE1\\u606F\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"worldName\",\n              label: \"\\u4E16\\u754C\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u4E5D\\u5DDE\\u5927\\u9646\\u3001\\u4FEE\\u4ED9\\u754C\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"overview\",\n              label: \"\\u4E16\\u754C\\u6982\\u8FF0\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u6574\\u4F53\\u60C5\\u51B5\\u548C\\u7279\\u8272...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)]\n          }, \"basic\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5730\\u7406\\u73AF\\u5883\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"geography\",\n              label: \"\\u5730\\u7406\\u63CF\\u8FF0\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u5730\\u7406\\u73AF\\u5883\\u3001\\u5730\\u5F62\\u5730\\u8C8C...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"climate\",\n              label: \"\\u6C14\\u5019\\u73AF\\u5883\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u6C14\\u5019\\u7279\\u70B9\\u3001\\u5B63\\u8282\\u53D8\\u5316...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, \"geography\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5386\\u53F2\\u6587\\u5316\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"history\",\n              label: \"\\u5386\\u53F2\\u80CC\\u666F\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u5386\\u53F2\\u53D1\\u5C55\\u3001\\u91CD\\u5927\\u4E8B\\u4EF6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"culture\",\n              label: \"\\u6587\\u5316\\u7279\\u8272\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u6587\\u5316\\u4F20\\u7EDF\\u3001\\u4EF7\\u503C\\u89C2\\u5FF5...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, \"history\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u653F\\u6CBB\\u7ECF\\u6D4E\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"politics\",\n              label: \"\\u653F\\u6CBB\\u4F53\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u653F\\u6CBB\\u5236\\u5EA6\\u3001\\u6743\\u529B\\u7ED3\\u6784...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"economy\",\n              label: \"\\u7ECF\\u6D4E\\u5236\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7ECF\\u6D4E\\u4F53\\u7CFB\\u3001\\u8D27\\u5E01\\u5236\\u5EA6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)]\n          }, \"politics\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u8D27\\u5E01\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"currencyName\",\n              label: \"\\u4E3B\\u8981\\u8D27\\u5E01\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u7075\\u77F3\\u3001\\u91D1\\u5E01\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"currencySystem\",\n              label: \"\\u8D27\\u5E01\\u5236\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u8D27\\u5E01\\u5236\\u5EA6\\u3001\\u6C47\\u7387\\u4F53\\u7CFB...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"monetaryPolicy\",\n              label: \"\\u8D27\\u5E01\\u653F\\u7B56\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u53D1\\u884C\\u673A\\u6784\\u3001\\u76D1\\u7BA1\\u5236\\u5EA6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, \"currency\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5546\\u4E1A\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"tradeRoutes\",\n              label: \"\\u8D38\\u6613\\u8DEF\\u7EBF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4E3B\\u8981\\u8D38\\u6613\\u8DEF\\u7EBF\\u3001\\u5546\\u4E1A\\u7F51\\u7EDC...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"guilds\",\n              label: \"\\u5546\\u4F1A\\u7EC4\\u7EC7\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5546\\u4F1A\\u3001\\u516C\\u4F1A\\u7B49\\u5546\\u4E1A\\u7EC4\\u7EC7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"marketRules\",\n              label: \"\\u5E02\\u573A\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5E02\\u573A\\u673A\\u5236\\u3001\\u5546\\u4E1A\\u6CD5\\u89C4...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)]\n          }, \"commerce\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u79CD\\u65CF\\u7C7B\\u522B\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"majorRaces\",\n              label: \"\\u4E3B\\u8981\\u79CD\\u65CF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u4E2D\\u7684\\u4E3B\\u8981\\u79CD\\u65CF...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"raceRelations\",\n              label: \"\\u79CD\\u65CF\\u5173\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u79CD\\u65CF\\u95F4\\u7684\\u5173\\u7CFB\\u3001\\u51B2\\u7A81...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"raceTraits\",\n              label: \"\\u79CD\\u65CF\\u7279\\u5F81\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5404\\u79CD\\u65CF\\u7684\\u7279\\u6B8A\\u80FD\\u529B\\u3001\\u6587\\u5316...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)]\n          }, \"races\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u529F\\u6CD5\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"techniqueTypes\",\n              label: \"\\u529F\\u6CD5\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u529F\\u6CD5\\u7684\\u5206\\u7C7B\\u3001\\u7B49\\u7EA7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"cultivationMethods\",\n              label: \"\\u4FEE\\u70BC\\u65B9\\u6CD5\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4FEE\\u70BC\\u65B9\\u5F0F\\u3001\\u8D44\\u6E90\\u9700\\u6C42...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"martialArtsRules\",\n              label: \"\\u529F\\u6CD5\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u529F\\u6CD5\\u7684\\u9650\\u5236\\u3001\\u7981\\u5FCC...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, \"martial_arts\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7279\\u6B8A\\u89C4\\u5219\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"rules\",\n              label: \"\\u4E16\\u754C\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u7279\\u6B8A\\u89C4\\u5219\\u3001\\u6CD5\\u5219...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 600,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"magic\",\n              label: \"\\u529B\\u91CF\\u4F53\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4FEE\\u70BC\\u3001\\u9B54\\u6CD5\\u7B49\\u529B\\u91CF\\u4F53\\u7CFB...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this)]\n          }, \"rules\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4E16\\u754C\\u8BBE\\u5B9A\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 11\n      }, this)],\n      width: 1000,\n      children: viewingSetting && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              children: viewingSetting.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: categoryConfig[viewingSetting.category].color,\n                children: categoryConfig[viewingSetting.category].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: viewingSetting.status === 'active' ? 'green' : 'orange',\n                children: viewingSetting.status === 'active' ? '已完成' : '草稿'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginTop: 16\n              },\n              children: viewingSetting.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n          defaultActiveKey: ['1'],\n          children: [viewingSetting.content.worldName && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u4E16\\u754C\\u540D\\u79F0\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              children: viewingSetting.content.worldName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this)\n          }, \"1\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 17\n          }, this), viewingSetting.content.geography && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5730\\u7406\\u73AF\\u5883\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.geography\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 19\n            }, this)\n          }, \"2\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 17\n          }, this), viewingSetting.content.history && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5386\\u53F2\\u80CC\\u666F\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.history\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 19\n            }, this)\n          }, \"3\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 17\n          }, this), viewingSetting.content.politics && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u653F\\u6CBB\\u4F53\\u7CFB\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.politics\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 19\n            }, this)\n          }, \"4\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 17\n          }, this), viewingSetting.content.economy && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u7ECF\\u6D4E\\u5236\\u5EA6\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.economy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 19\n            }, this)\n          }, \"5\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 17\n          }, this), viewingSetting.content.culture && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u6587\\u5316\\u7279\\u8272\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.culture\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 19\n            }, this)\n          }, \"6\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 17\n          }, this), viewingSetting.content.currencySystem && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u8D27\\u5E01\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.currencySystem\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 19\n            }, this), viewingSetting.content.currencyName && /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: [\"\\u4E3B\\u8981\\u8D27\\u5E01\\uFF1A\", viewingSetting.content.currencyName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 21\n            }, this)]\n          }, \"7\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 17\n          }, this), viewingSetting.content.tradeRoutes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5546\\u4E1A\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.tradeRoutes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 19\n            }, this), viewingSetting.content.guilds && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5546\\u4F1A\\u7EC4\\u7EC7\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 32\n              }, this), viewingSetting.content.guilds]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 21\n            }, this)]\n          }, \"8\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 17\n          }, this), viewingSetting.content.majorRaces && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u79CD\\u65CF\\u7C7B\\u522B\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.majorRaces\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 19\n            }, this), viewingSetting.content.raceRelations && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u79CD\\u65CF\\u5173\\u7CFB\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 32\n              }, this), viewingSetting.content.raceRelations]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 21\n            }, this)]\n          }, \"9\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 17\n          }, this), viewingSetting.content.techniqueTypes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u529F\\u6CD5\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.techniqueTypes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 19\n            }, this), viewingSetting.content.cultivationMethods && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u4FEE\\u70BC\\u65B9\\u6CD5\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 32\n              }, this), viewingSetting.content.cultivationMethods]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 21\n            }, this)]\n          }, \"10\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 17\n          }, this), viewingSetting.content.rules && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u4E16\\u754C\\u89C4\\u5219\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.rules\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 19\n            }, this)\n          }, \"11\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 308,\n    columnNumber: 5\n  }, this);\n};\n_s(WorldSettings, \"/Ahf01ykd3KMvsM1U7aStmquoFE=\", false, function () {\n  return [Form.useForm];\n});\n_c = WorldSettings;\nexport default WorldSettings;\nvar _c;\n$RefreshReg$(_c, \"WorldSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "Tabs", "Collapse", "PlusOutlined", "GlobalOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "EnvironmentOutlined", "HistoryOutlined", "BankOutlined", "TeamOutlined", "BookOutlined", "DollarOutlined", "ShopOutlined", "UsergroupAddOutlined", "ThunderboltOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "TabPane", "Panel", "WorldSettings", "_s", "settings", "setSettings", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingSetting", "setEditingSetting", "viewingSetting", "setViewingSetting", "form", "useForm", "mockSettings", "id", "name", "category", "description", "content", "worldName", "geography", "history", "politics", "economy", "culture", "status", "createdAt", "updatedAt", "realms", "methods", "resources", "bottlenecks", "lifespan", "majorSects", "relationships", "territories", "hierarchy", "categoryConfig", "world_basic", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currency", "commerce", "races", "martial_arts", "cultivation", "factions", "other", "columns", "title", "dataIndex", "key", "render", "record", "children", "strong", "filters", "Object", "keys", "map", "value", "onFilter", "ellipsis", "sorter", "a", "b", "Date", "_", "type", "onClick", "handleView", "handleEdit", "handleAIGenerate", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleCreateOrEdit", "resetFields", "setting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "filter", "s", "success", "handleModalOk", "values", "validateFields", "processedValues", "toISOString", "split", "newSetting", "now", "error", "console", "totalSettings", "length", "activeSettings", "draftSettings", "categoryStats", "reduce", "acc", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "confirmLoading", "width", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "rows", "defaultActiveKey", "tab", "footer", "marginTop", "header", "currencySystem", "currencyName", "tradeRoutes", "guilds", "majorRaces", "raceRelations", "techniqueTypes", "cultivationMethods", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/WorldSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  Tabs,\n  Collapse\n} from 'antd';\nimport {\n  PlusOutlined,\n  GlobalOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  EnvironmentOutlined,\n  HistoryOutlined,\n  BankOutlined,\n  TeamOutlined,\n  BookOutlined,\n  DollarOutlined,\n  ShopOutlined,\n  UsergroupAddOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\nconst { Panel } = Collapse;\n\nconst WorldSettings = () => {\n  const [settings, setSettings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingSetting, setEditingSetting] = useState(null);\n  const [viewingSetting, setViewingSetting] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟世界设定数据\n  const mockSettings = [\n    {\n      id: 1,\n      name: '修仙世界基础设定',\n      category: 'world_basic',\n      description: '修仙世界的基本世界观和规则',\n      content: {\n        worldName: '九州大陆',\n        geography: '九州大陆分为九个州，每州都有不同的地理环境和修炼资源',\n        history: '上古时期，仙魔大战，仙界封印，修仙者只能在凡间修炼',\n        politics: '各大宗门割据一方，皇朝统治凡人，修仙者超然物外',\n        economy: '以灵石为主要货币，凡人使用金银铜钱',\n        culture: '尊师重道，强者为尊，追求长生不老'\n      },\n      status: 'active',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    },\n    {\n      id: 2,\n      name: '修炼体系',\n      category: 'cultivation',\n      description: '详细的修炼境界和体系',\n      content: {\n        realms: ['练气期', '筑基期', '金丹期', '元婴期', '化神期', '合体期', '大乘期', '渡劫期'],\n        methods: '吸收天地灵气，炼化为真元，淬炼肉身和神魂',\n        resources: '灵石、丹药、法器、功法、灵草',\n        bottlenecks: '每个大境界都有天劫考验',\n        lifespan: '每提升一个大境界，寿命大幅增加'\n      },\n      status: 'active',\n      createdAt: '2024-01-17',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      name: '宗门势力',\n      category: 'factions',\n      description: '各大宗门和势力的详细设定',\n      content: {\n        majorSects: ['青云宗', '天剑门', '万花谷', '血煞门'],\n        relationships: '正邪对立，内部也有竞争',\n        territories: '各占一方，互不侵犯',\n        resources: '控制灵脉和修炼资源',\n        hierarchy: '宗主、长老、内门弟子、外门弟子'\n      },\n      status: 'draft',\n      createdAt: '2024-01-19',\n      updatedAt: '2024-01-19'\n    }\n  ];\n\n  useEffect(() => {\n    setSettings(mockSettings);\n  }, []);\n\n  // 设定类型配置\n  const categoryConfig = {\n    world_basic: { color: 'blue', text: '世界基础', icon: <GlobalOutlined /> },\n    geography: { color: 'green', text: '地理环境', icon: <EnvironmentOutlined /> },\n    history: { color: 'orange', text: '历史文化', icon: <HistoryOutlined /> },\n    politics: { color: 'red', text: '政治体系', icon: <BankOutlined /> },\n    currency: { color: 'gold', text: '货币体系', icon: <DollarOutlined /> },\n    commerce: { color: 'lime', text: '商业体系', icon: <ShopOutlined /> },\n    races: { color: 'magenta', text: '种族类别', icon: <UsergroupAddOutlined /> },\n    martial_arts: { color: 'volcano', text: '功法体系', icon: <ThunderboltOutlined /> },\n    cultivation: { color: 'purple', text: '修炼体系', icon: <BookOutlined /> },\n    factions: { color: 'cyan', text: '势力组织', icon: <TeamOutlined /> },\n    economy: { color: 'geekblue', text: '经济制度', icon: <BankOutlined /> },\n    other: { color: 'default', text: '其他', icon: <GlobalOutlined /> }\n  };\n\n  // 表格列配置\n  const columns = [\n    {\n      title: '设定名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          {categoryConfig[record.category].icon}\n          <Text strong>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '类型',\n      dataIndex: 'category',\n      key: 'category',\n      render: (category) => (\n        <Tag color={categoryConfig[category].color}>\n          {categoryConfig[category].text}\n        </Tag>\n      ),\n      filters: Object.keys(categoryConfig).map(key => ({\n        text: categoryConfig[key].text,\n        value: key\n      })),\n      onFilter: (value, record) => record.category === value\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={status === 'active' ? 'green' : 'orange'}>\n          {status === 'active' ? '已完成' : '草稿'}\n        </Tag>\n      )\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerate(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个设定吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理新建/编辑设定\n  const handleCreateOrEdit = () => {\n    setEditingSetting(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (setting) => {\n    setEditingSetting(setting);\n    form.setFieldsValue({\n      ...setting,\n      ...setting.content\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (setting) => {\n    setViewingSetting(setting);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (setting) => {\n    message.info(`AI生成世界设定：${setting.name}`);\n  };\n\n  const handleDelete = (id) => {\n    setSettings(settings.filter(s => s.id !== id));\n    message.success('设定删除成功');\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 分离基本信息和内容\n      const { name, category, description, status, ...content } = values;\n      const processedValues = {\n        name,\n        category,\n        description,\n        status,\n        content\n      };\n\n      if (editingSetting) {\n        // 编辑设定\n        setSettings(settings.map(s =>\n          s.id === editingSetting.id\n            ? { ...s, ...processedValues, updatedAt: new Date().toISOString().split('T')[0] }\n            : s\n        ));\n        message.success('设定更新成功');\n      } else {\n        // 新建设定\n        const newSetting = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setSettings([...settings, newSetting]);\n        message.success('设定创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalSettings = settings.length;\n  const activeSettings = settings.filter(s => s.status === 'active').length;\n  const draftSettings = settings.filter(s => s.status === 'draft').length;\n  const categoryStats = Object.keys(categoryConfig).reduce((acc, key) => {\n    acc[key] = settings.filter(s => s.category === key).length;\n    return acc;\n  }, {});\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">世界设定</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总设定数\"\n              value={totalSettings}\n              prefix={<GlobalOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={activeSettings}\n              prefix={<BookOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"草稿\"\n              value={draftSettings}\n              prefix={<EditOutlined style={{ color: '#faad14' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"修炼体系\"\n              value={categoryStats.cultivation || 0}\n              prefix={<BookOutlined style={{ color: '#722ed1' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加设定\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={settings}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个设定`\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑设定模态框 */}\n      <Modal\n        title={editingSetting ? '编辑世界设定' : '新建世界设定'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={1000}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            category: 'world_basic',\n            status: 'draft'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"设定名称\"\n                rules={[{ required: true, message: '请输入设定名称' }]}\n              >\n                <Input placeholder=\"请输入设定名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"设定类型\"\n                rules={[{ required: true, message: '请选择设定类型' }]}\n              >\n                <Select>\n                  {Object.keys(categoryConfig).map(key => (\n                    <Option key={key} value={key}>\n                      {categoryConfig[key].text}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"设定描述\"\n            rules={[{ required: true, message: '请输入设定描述' }]}\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"简要描述这个设定的内容和作用\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择状态' }]}\n          >\n            <Select>\n              <Option value=\"draft\">草稿</Option>\n              <Option value=\"active\">已完成</Option>\n            </Select>\n          </Form.Item>\n\n          <Tabs defaultActiveKey=\"basic\">\n            <TabPane tab=\"基础信息\" key=\"basic\">\n              <Form.Item name=\"worldName\" label=\"世界名称\">\n                <Input placeholder=\"如：九州大陆、修仙界等\" />\n              </Form.Item>\n\n              <Form.Item name=\"overview\" label=\"世界概述\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的整体情况和特色...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"地理环境\" key=\"geography\">\n              <Form.Item name=\"geography\" label=\"地理描述\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的地理环境、地形地貌...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"climate\" label=\"气候环境\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述气候特点、季节变化...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"历史文化\" key=\"history\">\n              <Form.Item name=\"history\" label=\"历史背景\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的历史发展、重大事件...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"culture\" label=\"文化特色\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述文化传统、价值观念...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"政治经济\" key=\"politics\">\n              <Form.Item name=\"politics\" label=\"政治体系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述政治制度、权力结构...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"economy\" label=\"经济制度\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述经济体系、货币制度...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"货币体系\" key=\"currency\">\n              <Form.Item name=\"currencyName\" label=\"主要货币\">\n                <Input placeholder=\"如：灵石、金币等\" />\n              </Form.Item>\n\n              <Form.Item name=\"currencySystem\" label=\"货币制度\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述货币制度、汇率体系...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"monetaryPolicy\" label=\"货币政策\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述发行机构、监管制度...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"商业体系\" key=\"commerce\">\n              <Form.Item name=\"tradeRoutes\" label=\"贸易路线\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述主要贸易路线、商业网络...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"guilds\" label=\"商会组织\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述商会、公会等商业组织...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"marketRules\" label=\"市场规则\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述市场机制、商业法规...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"种族类别\" key=\"races\">\n              <Form.Item name=\"majorRaces\" label=\"主要种族\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述世界中的主要种族...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"raceRelations\" label=\"种族关系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述种族间的关系、冲突...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"raceTraits\" label=\"种族特征\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述各种族的特殊能力、文化...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"功法体系\" key=\"martial_arts\">\n              <Form.Item name=\"techniqueTypes\" label=\"功法分类\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述功法的分类、等级...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"cultivationMethods\" label=\"修炼方法\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述修炼方式、资源需求...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"martialArtsRules\" label=\"功法规则\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述功法的限制、禁忌...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"特殊规则\" key=\"rules\">\n              <Form.Item name=\"rules\" label=\"世界规则\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的特殊规则、法则...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"magic\" label=\"力量体系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述修炼、魔法等力量体系...\"\n                />\n              </Form.Item>\n            </TabPane>\n          </Tabs>\n        </Form>\n      </Modal>\n\n      {/* 设定详情模态框 */}\n      <Modal\n        title=\"世界设定详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={1000}\n      >\n        {viewingSetting && (\n          <div>\n            <Row gutter={16} style={{ marginBottom: 16 }}>\n              <Col span={24}>\n                <Title level={3}>{viewingSetting.name}</Title>\n                <Space>\n                  <Tag color={categoryConfig[viewingSetting.category].color}>\n                    {categoryConfig[viewingSetting.category].text}\n                  </Tag>\n                  <Tag color={viewingSetting.status === 'active' ? 'green' : 'orange'}>\n                    {viewingSetting.status === 'active' ? '已完成' : '草稿'}\n                  </Tag>\n                </Space>\n                <Paragraph style={{ marginTop: 16 }}>\n                  {viewingSetting.description}\n                </Paragraph>\n              </Col>\n            </Row>\n\n            <Collapse defaultActiveKey={['1']}>\n              {viewingSetting.content.worldName && (\n                <Panel header=\"世界名称\" key=\"1\">\n                  <Text>{viewingSetting.content.worldName}</Text>\n                </Panel>\n              )}\n\n              {viewingSetting.content.geography && (\n                <Panel header=\"地理环境\" key=\"2\">\n                  <Paragraph>{viewingSetting.content.geography}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.history && (\n                <Panel header=\"历史背景\" key=\"3\">\n                  <Paragraph>{viewingSetting.content.history}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.politics && (\n                <Panel header=\"政治体系\" key=\"4\">\n                  <Paragraph>{viewingSetting.content.politics}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.economy && (\n                <Panel header=\"经济制度\" key=\"5\">\n                  <Paragraph>{viewingSetting.content.economy}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.culture && (\n                <Panel header=\"文化特色\" key=\"6\">\n                  <Paragraph>{viewingSetting.content.culture}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.currencySystem && (\n                <Panel header=\"货币体系\" key=\"7\">\n                  <Paragraph>{viewingSetting.content.currencySystem}</Paragraph>\n                  {viewingSetting.content.currencyName && (\n                    <Text strong>主要货币：{viewingSetting.content.currencyName}</Text>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.tradeRoutes && (\n                <Panel header=\"商业体系\" key=\"8\">\n                  <Paragraph>{viewingSetting.content.tradeRoutes}</Paragraph>\n                  {viewingSetting.content.guilds && (\n                    <Paragraph><Text strong>商会组织：</Text>{viewingSetting.content.guilds}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.majorRaces && (\n                <Panel header=\"种族类别\" key=\"9\">\n                  <Paragraph>{viewingSetting.content.majorRaces}</Paragraph>\n                  {viewingSetting.content.raceRelations && (\n                    <Paragraph><Text strong>种族关系：</Text>{viewingSetting.content.raceRelations}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.techniqueTypes && (\n                <Panel header=\"功法体系\" key=\"10\">\n                  <Paragraph>{viewingSetting.content.techniqueTypes}</Paragraph>\n                  {viewingSetting.content.cultivationMethods && (\n                    <Paragraph><Text strong>修炼方法：</Text>{viewingSetting.content.cultivationMethods}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.rules && (\n                <Panel header=\"世界规则\" key=\"11\">\n                  <Paragraph>{viewingSetting.content.rules}</Paragraph>\n                </Panel>\n              )}\n            </Collapse>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorldSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,IAAI,EACJC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,mBAAmB,QACd,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGrC,UAAU;AAC7C,MAAM;EAAEsC;AAAS,CAAC,GAAG/B,KAAK;AAC1B,MAAM;EAAEgC;AAAO,CAAC,GAAG/B,MAAM;AACzB,MAAM;EAAEgC;AAAQ,CAAC,GAAGxB,IAAI;AACxB,MAAM;EAAEyB;AAAM,CAAC,GAAGxB,QAAQ;AAE1B,MAAMyB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,IAAI,CAAC,GAAGlD,IAAI,CAACmD,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,eAAe;IAC5BC,OAAO,EAAE;MACPC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,4BAA4B;MACvCC,OAAO,EAAE,2BAA2B;MACpCC,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,YAAY;IACzBC,OAAO,EAAE;MACPU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAChEC,OAAO,EAAE,sBAAsB;MAC/BC,SAAS,EAAE,gBAAgB;MAC3BC,WAAW,EAAE,aAAa;MAC1BC,QAAQ,EAAE;IACZ,CAAC;IACDP,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,cAAc;IAC3BC,OAAO,EAAE;MACPe,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACxCC,aAAa,EAAE,aAAa;MAC5BC,WAAW,EAAE,WAAW;MACxBL,SAAS,EAAE,WAAW;MACtBM,SAAS,EAAE;IACb,CAAC;IACDX,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED1E,SAAS,CAAC,MAAM;IACd+C,WAAW,CAACa,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwB,cAAc,GAAG;IACrBC,WAAW,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACf,cAAc;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACtEzB,SAAS,EAAE;MAAEmB,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACV,mBAAmB;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC1ExB,OAAO,EAAE;MAAEkB,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACT,eAAe;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACrEvB,QAAQ,EAAE;MAAEiB,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACR,YAAY;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAChEC,QAAQ,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACL,cAAc;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACnEE,QAAQ,EAAE;MAAER,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACJ,YAAY;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEG,KAAK,EAAE;MAAET,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACH,oBAAoB;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACzEI,YAAY,EAAE;MAAEV,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACF,mBAAmB;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC/EK,WAAW,EAAE;MAAEX,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACN,YAAY;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACtEM,QAAQ,EAAE;MAAEZ,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACP,YAAY;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEtB,OAAO,EAAE;MAAEgB,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACR,YAAY;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACpEO,KAAK,EAAE;MAAEb,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAEpD,OAAA,CAACf,cAAc;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EAClE,CAAC;;EAED;EACA,MAAMQ,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACjB,IAAI,EAAEkB,MAAM,kBACnBrE,OAAA,CAAC/B,KAAK;MAAAqG,QAAA,GACHtB,cAAc,CAACqB,MAAM,CAAC1C,QAAQ,CAAC,CAACyB,IAAI,eACrCpD,OAAA,CAACE,IAAI;QAACqE,MAAM;QAAAD,QAAA,EAAEnB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAEX,CAAC,EACD;IACES,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGzC,QAAQ,iBACf3B,OAAA,CAAC9B,GAAG;MAACgF,KAAK,EAAEF,cAAc,CAACrB,QAAQ,CAAC,CAACuB,KAAM;MAAAoB,QAAA,EACxCtB,cAAc,CAACrB,QAAQ,CAAC,CAACwB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACN;IACDgB,OAAO,EAAEC,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC,CAAC2B,GAAG,CAACR,GAAG,KAAK;MAC/ChB,IAAI,EAAEH,cAAc,CAACmB,GAAG,CAAC,CAAChB,IAAI;MAC9ByB,KAAK,EAAET;IACT,CAAC,CAAC,CAAC;IACHU,QAAQ,EAAEA,CAACD,KAAK,EAAEP,MAAM,KAAKA,MAAM,CAAC1C,QAAQ,KAAKiD;EACnD,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBW,QAAQ,EAAE;EACZ,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGhC,MAAM,iBACbpC,OAAA,CAAC9B,GAAG;MAACgF,KAAK,EAAEd,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAS;MAAAkC,QAAA,EAClDlC,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG;IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC;EAET,CAAC,EACD;IACES,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAAC1C,SAAS,CAAC,GAAG,IAAI4C,IAAI,CAACD,CAAC,CAAC3C,SAAS;EAChE,CAAC,EACD;IACE2B,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEd,MAAM,kBAChBrE,OAAA,CAAC/B,KAAK;MAAAqG,QAAA,gBACJtE,OAAA,CAACvB,OAAO;QAACwF,KAAK,EAAC,0BAAM;QAAAK,QAAA,eACnBtE,OAAA,CAACjC,MAAM;UACLqH,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAEpD,OAAA,CAACZ,WAAW;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB6B,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACjB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAACvB,OAAO;QAACwF,KAAK,EAAC,cAAI;QAAAK,QAAA,eACjBtE,OAAA,CAACjC,MAAM;UACLqH,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAEpD,OAAA,CAACd,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB6B,OAAO,EAAEA,CAAA,KAAME,UAAU,CAAClB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAACvB,OAAO;QAACwF,KAAK,EAAC,gBAAM;QAAAK,QAAA,eACnBtE,OAAA,CAACjC,MAAM;UACLqH,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAEpD,OAAA,CAACX,aAAa;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB6B,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAACnB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAACxB,UAAU;QACTyF,KAAK,EAAC,8DAAY;QAClBwB,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACrB,MAAM,CAAC5C,EAAE,CAAE;QACzCkE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAtB,QAAA,eAEftE,OAAA,CAACvB,OAAO;UAACwF,KAAK,EAAC,cAAI;UAAAK,QAAA,eACjBtE,OAAA,CAACjC,MAAM;YACLqH,IAAI,EAAC,MAAM;YACXS,MAAM;YACNzC,IAAI,eAAEpD,OAAA,CAACb,cAAc;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3E,iBAAiB,CAAC,IAAI,CAAC;IACvBG,IAAI,CAACyE,WAAW,CAAC,CAAC;IAClBhF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMwE,UAAU,GAAIS,OAAO,IAAK;IAC9B7E,iBAAiB,CAAC6E,OAAO,CAAC;IAC1B1E,IAAI,CAAC2E,cAAc,CAAC;MAClB,GAAGD,OAAO;MACV,GAAGA,OAAO,CAACnE;IACb,CAAC,CAAC;IACFd,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuE,UAAU,GAAIU,OAAO,IAAK;IAC9B3E,iBAAiB,CAAC2E,OAAO,CAAC;IAC1B/E,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuE,gBAAgB,GAAIQ,OAAO,IAAK;IACpCzH,OAAO,CAAC2H,IAAI,CAAC,YAAYF,OAAO,CAACtE,IAAI,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMgE,YAAY,GAAIjE,EAAE,IAAK;IAC3Bd,WAAW,CAACD,QAAQ,CAACyF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3E,EAAE,KAAKA,EAAE,CAAC,CAAC;IAC9ClD,OAAO,CAAC8H,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMjF,IAAI,CAACkF,cAAc,CAAC,CAAC;MAC1C3F,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM;QAAEa,IAAI;QAAEC,QAAQ;QAAEC,WAAW;QAAEQ,MAAM;QAAE,GAAGP;MAAQ,CAAC,GAAG0E,MAAM;MAClE,MAAME,eAAe,GAAG;QACtB/E,IAAI;QACJC,QAAQ;QACRC,WAAW;QACXQ,MAAM;QACNP;MACF,CAAC;MAED,IAAIX,cAAc,EAAE;QAClB;QACAP,WAAW,CAACD,QAAQ,CAACiE,GAAG,CAACyB,CAAC,IACxBA,CAAC,CAAC3E,EAAE,KAAKP,cAAc,CAACO,EAAE,GACtB;UAAE,GAAG2E,CAAC;UAAE,GAAGK,eAAe;UAAEnE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GAC/EP,CACN,CAAC,CAAC;QACF7H,OAAO,CAAC8H,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMO,UAAU,GAAG;UACjBnF,EAAE,EAAEyD,IAAI,CAAC2B,GAAG,CAAC,CAAC;UACd,GAAGJ,eAAe;UAClBpE,SAAS,EAAE,IAAI6C,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDrE,SAAS,EAAE,IAAI4C,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDhG,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEkG,UAAU,CAAC,CAAC;QACtCrI,OAAO,CAAC8H,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAtF,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAACyE,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRjG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmG,aAAa,GAAGtG,QAAQ,CAACuG,MAAM;EACrC,MAAMC,cAAc,GAAGxG,QAAQ,CAACyF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChE,MAAM,KAAK,QAAQ,CAAC,CAAC6E,MAAM;EACzE,MAAME,aAAa,GAAGzG,QAAQ,CAACyF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChE,MAAM,KAAK,OAAO,CAAC,CAAC6E,MAAM;EACvE,MAAMG,aAAa,GAAG3C,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEnD,GAAG,KAAK;IACrEmD,GAAG,CAACnD,GAAG,CAAC,GAAGzD,QAAQ,CAACyF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzE,QAAQ,KAAKwC,GAAG,CAAC,CAAC8C,MAAM;IAC1D,OAAOK,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,oBACEtH,OAAA;IAAKuH,SAAS,EAAC,SAAS;IAAAjD,QAAA,gBACtBtE,OAAA;MAAKuH,SAAS,EAAC,aAAa;MAAAjD,QAAA,eAC1BtE,OAAA,CAACC,KAAK;QAACuH,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAjD,QAAA,EAAC;MAAI;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNxD,OAAA,CAACtB,GAAG;MAAC+I,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArD,QAAA,gBAC3CtE,OAAA,CAACrB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACXtE,OAAA,CAACnC,IAAI;UAAAyG,QAAA,eACHtE,OAAA,CAACpB,SAAS;YACRqF,KAAK,EAAC,0BAAM;YACZW,KAAK,EAAEoC,aAAc;YACrBa,MAAM,eAAE7H,OAAA,CAACf,cAAc;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAACrB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACXtE,OAAA,CAACnC,IAAI;UAAAyG,QAAA,eACHtE,OAAA,CAACpB,SAAS;YACRqF,KAAK,EAAC,oBAAK;YACXW,KAAK,EAAEsC,cAAe;YACtBW,MAAM,eAAE7H,OAAA,CAACN,YAAY;cAACgI,KAAK,EAAE;gBAAExE,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAACrB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACXtE,OAAA,CAACnC,IAAI;UAAAyG,QAAA,eACHtE,OAAA,CAACpB,SAAS;YACRqF,KAAK,EAAC,cAAI;YACVW,KAAK,EAAEuC,aAAc;YACrBU,MAAM,eAAE7H,OAAA,CAACd,YAAY;cAACwI,KAAK,EAAE;gBAAExE,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAACrB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACXtE,OAAA,CAACnC,IAAI;UAAAyG,QAAA,eACHtE,OAAA,CAACpB,SAAS;YACRqF,KAAK,EAAC,0BAAM;YACZW,KAAK,EAAEwC,aAAa,CAACvD,WAAW,IAAI,CAAE;YACtCgE,MAAM,eAAE7H,OAAA,CAACN,YAAY;cAACgI,KAAK,EAAE;gBAAExE,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxD,OAAA,CAACnC,IAAI;MAAAyG,QAAA,gBACHtE,OAAA;QAAKuH,SAAS,EAAC,SAAS;QAAAjD,QAAA,eACtBtE,OAAA;UAAKuH,SAAS,EAAC,cAAc;UAAAjD,QAAA,eAC3BtE,OAAA,CAACjC,MAAM;YACLqH,IAAI,EAAC,SAAS;YACdhC,IAAI,eAAEpD,OAAA,CAAChB,YAAY;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB6B,OAAO,EAAES,kBAAmB;YAAAxB,QAAA,EAC7B;UAED;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA,CAAChC,KAAK;QACJgG,OAAO,EAAEA,OAAQ;QACjB8D,UAAU,EAAEpH,QAAS;QACrBqH,MAAM,EAAC,IAAI;QACXnH,OAAO,EAAEA,OAAQ;QACjBoH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAA/E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPxD,OAAA,CAAC7B,KAAK;MACJ8F,KAAK,EAAE/C,cAAc,GAAG,QAAQ,GAAG,QAAS;MAC5CmH,IAAI,EAAEvH,YAAa;MACnBwH,IAAI,EAAEhC,aAAc;MACpBiC,QAAQ,EAAEA,CAAA,KAAM;QACdxH,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAACyE,WAAW,CAAC,CAAC;MACpB,CAAE;MACFyC,cAAc,EAAE5H,OAAQ;MACxB6H,KAAK,EAAE,IAAK;MAAAnE,QAAA,eAEZtE,OAAA,CAAC5B,IAAI;QACHkD,IAAI,EAAEA,IAAK;QACXoH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbhH,QAAQ,EAAE,aAAa;UACvBS,MAAM,EAAE;QACV,CAAE;QAAAkC,QAAA,gBAEFtE,OAAA,CAACtB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAAnD,QAAA,gBACdtE,OAAA,CAACrB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cACRlH,IAAI,EAAC,MAAM;cACXmH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAExK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA+F,QAAA,eAEhDtE,OAAA,CAAC3B,KAAK;gBAAC2K,WAAW,EAAC;cAAS;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxD,OAAA,CAACrB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cACRlH,IAAI,EAAC,UAAU;cACfmH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAExK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA+F,QAAA,eAEhDtE,OAAA,CAAC1B,MAAM;gBAAAgG,QAAA,EACJG,MAAM,CAACC,IAAI,CAAC1B,cAAc,CAAC,CAAC2B,GAAG,CAACR,GAAG,iBAClCnE,OAAA,CAACK,MAAM;kBAAWuE,KAAK,EAAET,GAAI;kBAAAG,QAAA,EAC1BtB,cAAc,CAACmB,GAAG,CAAC,CAAChB;gBAAI,GADdgB,GAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;UACRlH,IAAI,EAAC,aAAa;UAClBmH,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA+F,QAAA,eAEhDtE,OAAA,CAACI,QAAQ;YACP6I,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAgB;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;UACRlH,IAAI,EAAC,QAAQ;UACbmH,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA+F,QAAA,eAE9CtE,OAAA,CAAC1B,MAAM;YAAAgG,QAAA,gBACLtE,OAAA,CAACK,MAAM;cAACuE,KAAK,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCxD,OAAA,CAACK,MAAM;cAACuE,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZxD,OAAA,CAAClB,IAAI;UAACoK,gBAAgB,EAAC,OAAO;UAAA5E,QAAA,gBAC5BtE,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,WAAW;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACtCtE,OAAA,CAAC3B,KAAK;gBAAC2K,WAAW,EAAC;cAAa;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,UAAU;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACrCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAVU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWtB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,WAAW;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACtCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAmB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,SAAS;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,WAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAc1B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,SAAS;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAmB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,SAAS;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,SAAS;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcxB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,UAAU;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACrCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,SAAS;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAczB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,cAAc;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzCtE,OAAA,CAAC3B,KAAK;gBAAC2K,WAAW,EAAC;cAAU;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,gBAAgB;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,gBAAgB;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAjBU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBzB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,aAAa;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACxCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAkB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,QAAQ;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACnCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,aAAa;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACxCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBzB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,YAAY;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACvCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,eAAe;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC1CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,YAAY;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACvCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAkB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBtB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,gBAAgB;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,oBAAoB;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC/CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,kBAAkB;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC7CtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,cAAc;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqB7B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAAC6I,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjBtE,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,OAAO;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAClCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAAC5B,IAAI,CAACwK,IAAI;cAAClH,IAAI,EAAC,OAAO;cAACmH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAClCtE,OAAA,CAACI,QAAQ;gBACP6I,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAA3F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OActB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRxD,OAAA,CAAC7B,KAAK;MACJ8F,KAAK,EAAC,sCAAQ;MACdoE,IAAI,EAAErH,kBAAmB;MACzBuH,QAAQ,EAAEA,CAAA,KAAMtH,qBAAqB,CAAC,KAAK,CAAE;MAC7CmI,MAAM,EAAE,cACNpJ,OAAA,CAACjC,MAAM;QAAasH,OAAO,EAAEA,CAAA,KAAMpE,qBAAqB,CAAC,KAAK,CAAE;QAAAqD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFiF,KAAK,EAAE,IAAK;MAAAnE,QAAA,EAEXlD,cAAc,iBACbpB,OAAA;QAAAsE,QAAA,gBACEtE,OAAA,CAACtB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAArD,QAAA,eAC3CtE,OAAA,CAACrB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAtD,QAAA,gBACZtE,OAAA,CAACC,KAAK;cAACuH,KAAK,EAAE,CAAE;cAAAlD,QAAA,EAAElD,cAAc,CAACM;YAAI;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CxD,OAAA,CAAC/B,KAAK;cAAAqG,QAAA,gBACJtE,OAAA,CAAC9B,GAAG;gBAACgF,KAAK,EAAEF,cAAc,CAAC5B,cAAc,CAACO,QAAQ,CAAC,CAACuB,KAAM;gBAAAoB,QAAA,EACvDtB,cAAc,CAAC5B,cAAc,CAACO,QAAQ,CAAC,CAACwB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNxD,OAAA,CAAC9B,GAAG;gBAACgF,KAAK,EAAE9B,cAAc,CAACgB,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAS;gBAAAkC,QAAA,EACjElD,cAAc,CAACgB,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACRxD,OAAA,CAACG,SAAS;cAACuH,KAAK,EAAE;gBAAE2B,SAAS,EAAE;cAAG,CAAE;cAAA/E,QAAA,EACjClD,cAAc,CAACQ;YAAW;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA,CAACjB,QAAQ;UAACmK,gBAAgB,EAAE,CAAC,GAAG,CAAE;UAAA5E,QAAA,GAC/BlD,cAAc,CAACS,OAAO,CAACC,SAAS,iBAC/B9B,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACE,IAAI;cAAAoE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACC;YAAS;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GADxB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACE,SAAS,iBAC/B/B,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACE;YAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADlC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACG,OAAO,iBAC7BhC,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACG;YAAO;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACI,QAAQ,iBAC9BjC,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACI;YAAQ;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADjC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACK,OAAO,iBAC7BlC,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACK;YAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACM,OAAO,iBAC7BnC,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACM;YAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC0H,cAAc,iBACpCvJ,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAAC0H;YAAc;cAAAlG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAAC2H,YAAY,iBAClCxJ,OAAA,CAACE,IAAI;cAACqE,MAAM;cAAAD,QAAA,GAAC,gCAAK,EAAClD,cAAc,CAACS,OAAO,CAAC2H,YAAY;YAAA;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAC9D;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC4H,WAAW,iBACjCzJ,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAAC4H;YAAW;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC1DpC,cAAc,CAACS,OAAO,CAAC6H,MAAM,iBAC5B1J,OAAA,CAACG,SAAS;cAAAmE,QAAA,gBAACtE,OAAA,CAACE,IAAI;gBAACqE,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAAC6H,MAAM;YAAA;cAAArG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC/E;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC8H,UAAU,iBAChC3J,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAAC8H;YAAU;cAAAtG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACzDpC,cAAc,CAACS,OAAO,CAAC+H,aAAa,iBACnC5J,OAAA,CAACG,SAAS;cAAAmE,QAAA,gBAACtE,OAAA,CAACE,IAAI;gBAACqE,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAAC+H,aAAa;YAAA;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACtF;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACgI,cAAc,iBACpC7J,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACgI;YAAc;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAACiI,kBAAkB,iBACxC9J,OAAA,CAACG,SAAS;cAAAmE,QAAA,gBAACtE,OAAA,CAACE,IAAI;gBAACqE,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACiI,kBAAkB;YAAA;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC3F;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACiH,KAAK,iBAC3B9I,OAAA,CAACO,KAAK;YAAC+I,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClBtE,OAAA,CAACG,SAAS;cAAAmE,QAAA,EAAElD,cAAc,CAACS,OAAO,CAACiH;YAAK;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GAD9B,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/C,EAAA,CA9qBID,aAAa;EAAA,QAOFpC,IAAI,CAACmD,OAAO;AAAA;AAAAwI,EAAA,GAPvBvJ,aAAa;AAgrBnB,eAAeA,aAAa;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}