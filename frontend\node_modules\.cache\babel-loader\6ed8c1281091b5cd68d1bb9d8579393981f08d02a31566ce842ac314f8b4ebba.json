{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ChapterDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Typography, Button, Form, Input, Select, message, Space, Tag, Row, Col, Statistic, Divider, Breadcrumb } from 'antd';\nimport { SaveOutlined, ArrowLeftOutlined, FileTextOutlined, RobotOutlined, EyeOutlined, EditOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ChapterDetail = () => {\n  _s();\n  const {\n    projectId,\n    volumeId,\n    chapterId\n  } = useParams();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [chapter, setChapter] = useState(null);\n  const [volume, setVolume] = useState(null);\n  const [editMode, setEditMode] = useState(false);\n\n  // 状态配置\n  const statusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    draft: {\n      color: 'orange',\n      text: '草稿'\n    },\n    writing: {\n      color: 'processing',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n\n  // 模拟数据\n  useEffect(() => {\n    // 模拟获取章节数据\n    const mockChapter = {\n      id: parseInt(chapterId),\n      volumeId: parseInt(volumeId),\n      title: '第一章：觉醒',\n      chapterNumber: 1,\n      content: `在这个充满灵气的世界里，少年林天踏上了修仙之路...\n\n这是一个神奇的世界，天地间充满了灵气，修炼者可以通过吸收灵气来提升自己的实力。\n\n林天从小就对修炼充满了向往，今天终于有机会踏上这条道路。\n\n师父告诉他：\"修炼之路虽然艰难，但只要有恒心，终能成就大道。\"\n\n林天点了点头，眼中闪烁着坚定的光芒。`,\n      outline: '主角初次接触修仙世界，遇到第一位师父，开始修炼之路',\n      summary: '林天踏上修仙之路，遇到师父，开始修炼',\n      wordCount: 156,\n      status: 'completed',\n      notes: '这一章主要是世界观的介绍和主角的出场',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    };\n    const mockVolume = {\n      id: parseInt(volumeId),\n      title: '第一卷：初入修仙界',\n      volumeNumber: 1\n    };\n    setChapter(mockChapter);\n    setVolume(mockVolume);\n    form.setFieldsValue(mockChapter);\n  }, [chapterId, volumeId, form]);\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 计算字数\n      const wordCount = values.content ? values.content.replace(/\\s/g, '').length : 0;\n      const updatedChapter = {\n        ...chapter,\n        ...values,\n        wordCount,\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setChapter(updatedChapter);\n      setEditMode(false);\n      message.success('章节保存成功');\n    } catch (error) {\n      console.error('保存失败:', error);\n      message.error('保存失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAIContinue = () => {\n    message.info('AI续写功能开发中...');\n  };\n  const handleBack = () => {\n    navigate(`/projects/${projectId}/volumes`);\n  };\n  if (!chapter || !volume) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          onClick: handleBack,\n          style: {\n            padding: 0\n          },\n          children: \"\\u5377\\u5B97\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: volume.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Breadcrumb.Item, {\n        children: chapter.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            className: \"page-title\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"text\",\n                icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 25\n                }, this),\n                onClick: handleBack\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), chapter.title, /*#__PURE__*/_jsxDEV(Tag, {\n                color: statusConfig[chapter.status].color,\n                children: statusConfig[chapter.status].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: [volume.title, \" - \\u7B2C\", chapter.chapterNumber, \"\\u7AE0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          children: editMode ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setEditMode(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 25\n              }, this),\n              loading: loading,\n              onClick: handleSave,\n              children: \"\\u4FDD\\u5B58\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 25\n              }, this),\n              onClick: handleAIContinue,\n              children: \"AI\\u7EED\\u5199\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 25\n              }, this),\n              onClick: () => setEditMode(true),\n              children: \"\\u7F16\\u8F91\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B57\\u6570\",\n            value: chapter.wordCount,\n            suffix: \"\\u5B57\",\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7AE0\\u8282\\u5E8F\\u53F7\",\n            value: chapter.chapterNumber,\n            prefix: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n            value: chapter.createdAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n            value: chapter.updatedAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7AE0\\u8282\\u5185\\u5BB9\",\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u7AE0\\u8282\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入章节标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7AE0\\u8282\\u6807\\u9898\",\n                disabled: !editMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"content\",\n              label: \"\\u6B63\\u6587\\u5185\\u5BB9\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 20,\n                placeholder: \"\\u5F00\\u59CB\\u5199\\u4F5C...\",\n                disabled: !editMode,\n                showCount: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"notes\",\n              label: \"\\u4F5C\\u8005\\u5907\\u6CE8\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u8BB0\\u5F55\\u521B\\u4F5C\\u601D\\u8DEF\\u3001\\u5F85\\u4FEE\\u6539\\u5185\\u5BB9\\u7B49...\",\n                disabled: !editMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7AE0\\u8282\\u4FE1\\u606F\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapterNumber\",\n              label: \"\\u7AE0\\u8282\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入章节序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\",\n                disabled: !editMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择章节状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                disabled: !editMode,\n                children: Object.entries(statusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: config.text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"outline\",\n              label: \"\\u7AE0\\u8282\\u5927\\u7EB2\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u7AE0\\u8282\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u60C5\\u8282\\u53D1\\u5C55\",\n                disabled: !editMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"summary\",\n              label: \"\\u7AE0\\u8282\\u6458\\u8981\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u4E00\\u53E5\\u8BDD\\u603B\\u7ED3\\u672C\\u7AE0\\u5185\\u5BB9\",\n                disabled: !editMode\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5199\\u4F5C\\u63D0\\u793A\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#666'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u5EFA\\u8BAE\\u6BCF\\u7AE0\\u5B57\\u6570\\u63A7\\u5236\\u57282000-5000\\u5B57\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u6CE8\\u610F\\u60C5\\u8282\\u7684\\u8FDE\\u8D2F\\u6027\\u548C\\u8282\\u594F\\u611F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u53EF\\u4EE5\\u4F7F\\u7528AI\\u7EED\\u5199\\u529F\\u80FD\\u8F85\\u52A9\\u521B\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 \\u53CA\\u65F6\\u4FDD\\u5B58\\u907F\\u514D\\u5185\\u5BB9\\u4E22\\u5931\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 5\n  }, this);\n};\n_s(ChapterDetail, \"WHj7j/QQ36zH6hxGf2A2o+g0QLw=\", false, function () {\n  return [useParams, useNavigate, Form.useForm];\n});\n_c = ChapterDetail;\nexport default ChapterDetail;\nvar _c;\n$RefreshReg$(_c, \"ChapterDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Card", "Typography", "<PERSON><PERSON>", "Form", "Input", "Select", "message", "Space", "Tag", "Row", "Col", "Statistic", "Divider", "Breadcrumb", "SaveOutlined", "ArrowLeftOutlined", "FileTextOutlined", "RobotOutlined", "EyeOutlined", "EditOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "TextArea", "Option", "ChapterDetail", "_s", "projectId", "volumeId", "chapterId", "navigate", "form", "useForm", "loading", "setLoading", "chapter", "set<PERSON>hapter", "volume", "setVolume", "editMode", "setEditMode", "statusConfig", "planning", "color", "text", "draft", "writing", "completed", "published", "mockChapter", "id", "parseInt", "title", "chapterNumber", "content", "outline", "summary", "wordCount", "status", "notes", "createdAt", "updatedAt", "mockVolume", "volumeNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSave", "values", "validateFields", "replace", "length", "updatedChapter", "Date", "toISOString", "split", "success", "error", "console", "handleAIContinue", "info", "handleBack", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "marginBottom", "<PERSON><PERSON>", "type", "onClick", "padding", "display", "justifyContent", "alignItems", "level", "icon", "gutter", "span", "value", "suffix", "prefix", "layout", "name", "label", "rules", "required", "placeholder", "disabled", "rows", "showCount", "Object", "entries", "map", "key", "config", "size", "fontSize", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ChapterDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Typography,\n  Button,\n  Form,\n  Input,\n  Select,\n  message,\n  Space,\n  Tag,\n  Row,\n  Col,\n  Statistic,\n  Divider,\n  Breadcrumb\n} from 'antd';\nimport {\n  SaveOutlined,\n  ArrowLeftOutlined,\n  FileTextOutlined,\n  RobotOutlined,\n  EyeOutlined,\n  EditOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ChapterDetail = () => {\n  const { projectId, volumeId, chapterId } = useParams();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [chapter, setChapter] = useState(null);\n  const [volume, setVolume] = useState(null);\n  const [editMode, setEditMode] = useState(false);\n\n  // 状态配置\n  const statusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    draft: { color: 'orange', text: '草稿' },\n    writing: { color: 'processing', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  // 模拟数据\n  useEffect(() => {\n    // 模拟获取章节数据\n    const mockChapter = {\n      id: parseInt(chapterId),\n      volumeId: parseInt(volumeId),\n      title: '第一章：觉醒',\n      chapterNumber: 1,\n      content: `在这个充满灵气的世界里，少年林天踏上了修仙之路...\n\n这是一个神奇的世界，天地间充满了灵气，修炼者可以通过吸收灵气来提升自己的实力。\n\n林天从小就对修炼充满了向往，今天终于有机会踏上这条道路。\n\n师父告诉他：\"修炼之路虽然艰难，但只要有恒心，终能成就大道。\"\n\n林天点了点头，眼中闪烁着坚定的光芒。`,\n      outline: '主角初次接触修仙世界，遇到第一位师父，开始修炼之路',\n      summary: '林天踏上修仙之路，遇到师父，开始修炼',\n      wordCount: 156,\n      status: 'completed',\n      notes: '这一章主要是世界观的介绍和主角的出场',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    };\n\n    const mockVolume = {\n      id: parseInt(volumeId),\n      title: '第一卷：初入修仙界',\n      volumeNumber: 1\n    };\n\n    setChapter(mockChapter);\n    setVolume(mockVolume);\n    form.setFieldsValue(mockChapter);\n  }, [chapterId, volumeId, form]);\n\n  const handleSave = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 计算字数\n      const wordCount = values.content ? values.content.replace(/\\s/g, '').length : 0;\n      \n      const updatedChapter = {\n        ...chapter,\n        ...values,\n        wordCount,\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n\n      setChapter(updatedChapter);\n      setEditMode(false);\n      message.success('章节保存成功');\n    } catch (error) {\n      console.error('保存失败:', error);\n      message.error('保存失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAIContinue = () => {\n    message.info('AI续写功能开发中...');\n  };\n\n  const handleBack = () => {\n    navigate(`/projects/${projectId}/volumes`);\n  };\n\n  if (!chapter || !volume) {\n    return <div>加载中...</div>;\n  }\n\n  return (\n    <div className=\"fade-in\">\n      {/* 面包屑导航 */}\n      <Breadcrumb style={{ marginBottom: 16 }}>\n        <Breadcrumb.Item>\n          <Button type=\"link\" onClick={handleBack} style={{ padding: 0 }}>\n            卷宗管理\n          </Button>\n        </Breadcrumb.Item>\n        <Breadcrumb.Item>{volume.title}</Breadcrumb.Item>\n        <Breadcrumb.Item>{chapter.title}</Breadcrumb.Item>\n      </Breadcrumb>\n\n      <div className=\"page-header\">\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div>\n            <Title level={2} className=\"page-title\">\n              <Space>\n                <Button \n                  type=\"text\" \n                  icon={<ArrowLeftOutlined />} \n                  onClick={handleBack}\n                />\n                {chapter.title}\n                <Tag color={statusConfig[chapter.status].color}>\n                  {statusConfig[chapter.status].text}\n                </Tag>\n              </Space>\n            </Title>\n            <Text type=\"secondary\">\n              {volume.title} - 第{chapter.chapterNumber}章\n            </Text>\n          </div>\n          <Space>\n            {editMode ? (\n              <>\n                <Button onClick={() => setEditMode(false)}>取消</Button>\n                <Button \n                  type=\"primary\" \n                  icon={<SaveOutlined />}\n                  loading={loading}\n                  onClick={handleSave}\n                >\n                  保存\n                </Button>\n              </>\n            ) : (\n              <>\n                <Button \n                  icon={<RobotOutlined />}\n                  onClick={handleAIContinue}\n                >\n                  AI续写\n                </Button>\n                <Button \n                  type=\"primary\"\n                  icon={<EditOutlined />}\n                  onClick={() => setEditMode(true)}\n                >\n                  编辑\n                </Button>\n              </>\n            )}\n          </Space>\n        </div>\n      </div>\n\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"字数\"\n              value={chapter.wordCount}\n              suffix=\"字\"\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"章节序号\"\n              value={chapter.chapterNumber}\n              prefix={<EyeOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={chapter.createdAt}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"更新时间\"\n              value={chapter.updatedAt}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={16}>\n        {/* 章节内容 */}\n        <Col span={16}>\n          <Card title=\"章节内容\">\n            <Form form={form} layout=\"vertical\">\n              <Form.Item\n                name=\"title\"\n                label=\"章节标题\"\n                rules={[{ required: true, message: '请输入章节标题' }]}\n              >\n                <Input \n                  placeholder=\"请输入章节标题\" \n                  disabled={!editMode}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"content\"\n                label=\"正文内容\"\n              >\n                <TextArea\n                  rows={20}\n                  placeholder=\"开始写作...\"\n                  disabled={!editMode}\n                  showCount\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"notes\"\n                label=\"作者备注\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"记录创作思路、待修改内容等...\"\n                  disabled={!editMode}\n                />\n              </Form.Item>\n            </Form>\n          </Card>\n        </Col>\n\n        {/* 章节信息 */}\n        <Col span={8}>\n          <Card title=\"章节信息\" style={{ marginBottom: 16 }}>\n            <Form form={form} layout=\"vertical\">\n              <Form.Item\n                name=\"chapterNumber\"\n                label=\"章节序号\"\n                rules={[{ required: true, message: '请输入章节序号' }]}\n              >\n                <Input \n                  type=\"number\" \n                  placeholder=\"1\" \n                  disabled={!editMode}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择章节状态' }]}\n              >\n                <Select disabled={!editMode}>\n                  {Object.entries(statusConfig).map(([key, config]) => (\n                    <Option key={key} value={key}>{config.text}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n\n              <Form.Item\n                name=\"outline\"\n                label=\"章节大纲\"\n              >\n                <TextArea\n                  rows={4}\n                  placeholder=\"请简要描述本章节的主要内容和情节发展\"\n                  disabled={!editMode}\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"summary\"\n                label=\"章节摘要\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"一句话总结本章内容\"\n                  disabled={!editMode}\n                />\n              </Form.Item>\n            </Form>\n          </Card>\n\n          {/* 写作提示 */}\n          <Card title=\"写作提示\" size=\"small\">\n            <div style={{ fontSize: '12px', color: '#666' }}>\n              <p>• 建议每章字数控制在2000-5000字</p>\n              <p>• 注意情节的连贯性和节奏感</p>\n              <p>• 可以使用AI续写功能辅助创作</p>\n              <p>• 及时保存避免内容丢失</p>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default ChapterDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,OAAO,EACPC,UAAU,QACL,MAAM;AACb,SACEC,YAAY,EACZC,iBAAiB,EACjBC,gBAAgB,EAChBC,aAAa,EACbC,WAAW,EACXC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGxB,UAAU;AAClC,MAAM;EAAEyB;AAAS,CAAC,GAAGtB,KAAK;AAC1B,MAAM;EAAEuB;AAAO,CAAC,GAAGtB,MAAM;AAEzB,MAAMuB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC;EAAU,CAAC,GAAGlC,SAAS,CAAC,CAAC;EACtD,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAMgD,YAAY,GAAG;IACnBC,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCC,KAAK,EAAE;MAAEF,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCE,OAAO,EAAE;MAAEH,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC7CG,SAAS,EAAE;MAAEJ,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CI,SAAS,EAAE;MAAEL,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;;EAED;EACAlD,SAAS,CAAC,MAAM;IACd;IACA,MAAMuD,WAAW,GAAG;MAClBC,EAAE,EAAEC,QAAQ,CAACtB,SAAS,CAAC;MACvBD,QAAQ,EAAEuB,QAAQ,CAACvB,QAAQ,CAAC;MAC5BwB,KAAK,EAAE,QAAQ;MACfC,aAAa,EAAE,CAAC;MAChBC,OAAO,EAAE;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;MACbC,OAAO,EAAE,2BAA2B;MACpCC,OAAO,EAAE,oBAAoB;MAC7BC,SAAS,EAAE,GAAG;MACdC,MAAM,EAAE,WAAW;MACnBC,KAAK,EAAE,oBAAoB;MAC3BC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE;IACb,CAAC;IAED,MAAMC,UAAU,GAAG;MACjBZ,EAAE,EAAEC,QAAQ,CAACvB,QAAQ,CAAC;MACtBwB,KAAK,EAAE,WAAW;MAClBW,YAAY,EAAE;IAChB,CAAC;IAED3B,UAAU,CAACa,WAAW,CAAC;IACvBX,SAAS,CAACwB,UAAU,CAAC;IACrB/B,IAAI,CAACiC,cAAc,CAACf,WAAW,CAAC;EAClC,CAAC,EAAE,CAACpB,SAAS,EAAED,QAAQ,EAAEG,IAAI,CAAC,CAAC;EAE/B,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnC,IAAI,CAACoC,cAAc,CAAC,CAAC;MAC1CjC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuB,SAAS,GAAGS,MAAM,CAACZ,OAAO,GAAGY,MAAM,CAACZ,OAAO,CAACc,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,MAAM,GAAG,CAAC;MAE/E,MAAMC,cAAc,GAAG;QACrB,GAAGnC,OAAO;QACV,GAAG+B,MAAM;QACTT,SAAS;QACTI,SAAS,EAAE,IAAIU,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAClD,CAAC;MAEDrC,UAAU,CAACkC,cAAc,CAAC;MAC1B9B,WAAW,CAAC,KAAK,CAAC;MAClBrC,OAAO,CAACuE,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BxE,OAAO,CAACwE,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1E,OAAO,CAAC2E,IAAI,CAAC,cAAc,CAAC;EAC9B,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjD,QAAQ,CAAC,aAAaH,SAAS,UAAU,CAAC;EAC5C,CAAC;EAED,IAAI,CAACQ,OAAO,IAAI,CAACE,MAAM,EAAE;IACvB,oBAAOnB,OAAA;MAAA8D,QAAA,EAAK;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1B;EAEA,oBACElE,OAAA;IAAKmE,SAAS,EAAC,SAAS;IAAAL,QAAA,gBAEtB9D,OAAA,CAACR,UAAU;MAAC4E,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,gBACtC9D,OAAA,CAACR,UAAU,CAAC8E,IAAI;QAAAR,QAAA,eACd9D,OAAA,CAACnB,MAAM;UAAC0F,IAAI,EAAC,MAAM;UAACC,OAAO,EAAEX,UAAW;UAACO,KAAK,EAAE;YAAEK,OAAO,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAEhE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAClBlE,OAAA,CAACR,UAAU,CAAC8E,IAAI;QAAAR,QAAA,EAAE3C,MAAM,CAACe;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC,eACjDlE,OAAA,CAACR,UAAU,CAAC8E,IAAI;QAAAR,QAAA,EAAE7C,OAAO,CAACiB;MAAK;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAEblE,OAAA;MAAKmE,SAAS,EAAC,aAAa;MAAAL,QAAA,eAC1B9D,OAAA;QAAKoE,KAAK,EAAE;UAAEM,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACrF9D,OAAA;UAAA8D,QAAA,gBACE9D,OAAA,CAACG,KAAK;YAAC0E,KAAK,EAAE,CAAE;YAACV,SAAS,EAAC,YAAY;YAAAL,QAAA,eACrC9D,OAAA,CAACd,KAAK;cAAA4E,QAAA,gBACJ9D,OAAA,CAACnB,MAAM;gBACL0F,IAAI,EAAC,MAAM;gBACXO,IAAI,eAAE9E,OAAA,CAACN,iBAAiB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BM,OAAO,EAAEX;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,EACDjD,OAAO,CAACiB,KAAK,eACdlC,OAAA,CAACb,GAAG;gBAACsC,KAAK,EAAEF,YAAY,CAACN,OAAO,CAACuB,MAAM,CAAC,CAACf,KAAM;gBAAAqC,QAAA,EAC5CvC,YAAY,CAACN,OAAO,CAACuB,MAAM,CAAC,CAACd;cAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRlE,OAAA,CAACI,IAAI;YAACmE,IAAI,EAAC,WAAW;YAAAT,QAAA,GACnB3C,MAAM,CAACe,KAAK,EAAC,WAAI,EAACjB,OAAO,CAACkB,aAAa,EAAC,QAC3C;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNlE,OAAA,CAACd,KAAK;UAAA4E,QAAA,EACHzC,QAAQ,gBACPrB,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA,CAACnB,MAAM;cAAC2F,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,KAAK,CAAE;cAAAwC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtDlE,OAAA,CAACnB,MAAM;cACL0F,IAAI,EAAC,SAAS;cACdO,IAAI,eAAE9E,OAAA,CAACP,YAAY;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBnD,OAAO,EAAEA,OAAQ;cACjByD,OAAO,EAAEzB,UAAW;cAAAe,QAAA,EACrB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHlE,OAAA,CAAAE,SAAA;YAAA4D,QAAA,gBACE9D,OAAA,CAACnB,MAAM;cACLiG,IAAI,eAAE9E,OAAA,CAACJ,aAAa;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBM,OAAO,EAAEb,gBAAiB;cAAAG,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA,CAACnB,MAAM;cACL0F,IAAI,EAAC,SAAS;cACdO,IAAI,eAAE9E,OAAA,CAACF,YAAY;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBM,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,IAAI,CAAE;cAAAwC,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA,CAACZ,GAAG;MAAC2F,MAAM,EAAE,EAAG;MAACX,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,gBAC3C9D,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,CAAE;QAAAlB,QAAA,eACX9D,OAAA,CAACrB,IAAI;UAAAmF,QAAA,eACH9D,OAAA,CAACV,SAAS;YACR4C,KAAK,EAAC,cAAI;YACV+C,KAAK,EAAEhE,OAAO,CAACsB,SAAU;YACzB2C,MAAM,EAAC,QAAG;YACVC,MAAM,eAAEnF,OAAA,CAACL,gBAAgB;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,CAAE;QAAAlB,QAAA,eACX9D,OAAA,CAACrB,IAAI;UAAAmF,QAAA,eACH9D,OAAA,CAACV,SAAS;YACR4C,KAAK,EAAC,0BAAM;YACZ+C,KAAK,EAAEhE,OAAO,CAACkB,aAAc;YAC7BgD,MAAM,eAAEnF,OAAA,CAACH,WAAW;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,CAAE;QAAAlB,QAAA,eACX9D,OAAA,CAACrB,IAAI;UAAAmF,QAAA,eACH9D,OAAA,CAACV,SAAS;YACR4C,KAAK,EAAC,0BAAM;YACZ+C,KAAK,EAAEhE,OAAO,CAACyB;UAAU;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlE,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,CAAE;QAAAlB,QAAA,eACX9D,OAAA,CAACrB,IAAI;UAAAmF,QAAA,eACH9D,OAAA,CAACV,SAAS;YACR4C,KAAK,EAAC,0BAAM;YACZ+C,KAAK,EAAEhE,OAAO,CAAC0B;UAAU;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlE,OAAA,CAACZ,GAAG;MAAC2F,MAAM,EAAE,EAAG;MAAAjB,QAAA,gBAEd9D,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,EAAG;QAAAlB,QAAA,eACZ9D,OAAA,CAACrB,IAAI;UAACuD,KAAK,EAAC,0BAAM;UAAA4B,QAAA,eAChB9D,OAAA,CAAClB,IAAI;YAAC+B,IAAI,EAAEA,IAAK;YAACuE,MAAM,EAAC,UAAU;YAAAtB,QAAA,gBACjC9D,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6E,QAAA,eAEhD9D,OAAA,CAACjB,KAAK;gBACJ0G,WAAW,EAAC,4CAAS;gBACrBC,QAAQ,EAAE,CAACrE;cAAS;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlE,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,SAAS;cACdC,KAAK,EAAC,0BAAM;cAAAxB,QAAA,eAEZ9D,OAAA,CAACK,QAAQ;gBACPsF,IAAI,EAAE,EAAG;gBACTF,WAAW,EAAC,6BAAS;gBACrBC,QAAQ,EAAE,CAACrE,QAAS;gBACpBuE,SAAS;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlE,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cAAAxB,QAAA,eAEZ9D,OAAA,CAACK,QAAQ;gBACPsF,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,mFAAkB;gBAC9BC,QAAQ,EAAE,CAACrE;cAAS;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlE,OAAA,CAACX,GAAG;QAAC2F,IAAI,EAAE,CAAE;QAAAlB,QAAA,gBACX9D,OAAA,CAACrB,IAAI;UAACuD,KAAK,EAAC,0BAAM;UAACkC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAP,QAAA,eAC7C9D,OAAA,CAAClB,IAAI;YAAC+B,IAAI,EAAEA,IAAK;YAACuE,MAAM,EAAC,UAAU;YAAAtB,QAAA,gBACjC9D,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,eAAe;cACpBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6E,QAAA,eAEhD9D,OAAA,CAACjB,KAAK;gBACJwF,IAAI,EAAC,QAAQ;gBACbkB,WAAW,EAAC,GAAG;gBACfC,QAAQ,EAAE,CAACrE;cAAS;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlE,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvG,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6E,QAAA,eAEhD9D,OAAA,CAAChB,MAAM;gBAAC0G,QAAQ,EAAE,CAACrE,QAAS;gBAAAyC,QAAA,EACzB+B,MAAM,CAACC,OAAO,CAACvE,YAAY,CAAC,CAACwE,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,kBAC9CjG,OAAA,CAACM,MAAM;kBAAW2E,KAAK,EAAEe,GAAI;kBAAAlC,QAAA,EAAEmC,MAAM,CAACvE;gBAAI,GAA7BsE,GAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZlE,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,SAAS;cACdC,KAAK,EAAC,0BAAM;cAAAxB,QAAA,eAEZ9D,OAAA,CAACK,QAAQ;gBACPsF,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,8GAAoB;gBAChCC,QAAQ,EAAE,CAACrE;cAAS;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlE,OAAA,CAAClB,IAAI,CAACwF,IAAI;cACRe,IAAI,EAAC,SAAS;cACdC,KAAK,EAAC,0BAAM;cAAAxB,QAAA,eAEZ9D,OAAA,CAACK,QAAQ;gBACPsF,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,wDAAW;gBACvBC,QAAQ,EAAE,CAACrE;cAAS;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlE,OAAA,CAACrB,IAAI;UAACuD,KAAK,EAAC,0BAAM;UAACgE,IAAI,EAAC,OAAO;UAAApC,QAAA,eAC7B9D,OAAA;YAAKoE,KAAK,EAAE;cAAE+B,QAAQ,EAAE,MAAM;cAAE1E,KAAK,EAAE;YAAO,CAAE;YAAAqC,QAAA,gBAC9C9D,OAAA;cAAA8D,QAAA,EAAG;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5BlE,OAAA;cAAA8D,QAAA,EAAG;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrBlE,OAAA;cAAA8D,QAAA,EAAG;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACvBlE,OAAA;cAAA8D,QAAA,EAAG;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAlTID,aAAa;EAAA,QAC0B9B,SAAS,EACnCC,WAAW,EACbI,IAAI,CAACgC,OAAO;AAAA;AAAAsF,EAAA,GAHvB7F,aAAa;AAoTnB,eAAeA,aAAa;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}