{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\EquipmentSystems.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Rate, Statistic, Progress, Tabs } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, ShoppingOutlined, ThunderboltOutlined, SafetyOutlined, CrownOutlined, StarOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst EquipmentSystems = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [equipment, setEquipment] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingEquipment, setEditingEquipment] = useState(null);\n  const [activeTab, setActiveTab] = useState('weapon');\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockEquipment = [{\n    id: 1,\n    name: '龙鳞剑',\n    type: 'weapon',\n    category: 'sword',\n    grade: 'legendary',\n    level: 50,\n    attributes: {\n      attack: 1200,\n      durability: 800,\n      criticalRate: 15\n    },\n    requirements: {\n      level: 45,\n      strength: 200,\n      cultivation: '元婴期'\n    },\n    effects: ['龙威：攻击时有10%几率震慑敌人', '破甲：无视30%防御'],\n    materials: ['龙鳞', '玄铁', '灵石'],\n    description: '传说中的神兵，蕴含真龙之力',\n    rarity: 5,\n    value: 100000\n  }, {\n    id: 2,\n    name: '凤羽护甲',\n    type: 'armor',\n    category: 'chest',\n    grade: 'epic',\n    level: 40,\n    attributes: {\n      defense: 800,\n      durability: 600,\n      magicResist: 25\n    },\n    requirements: {\n      level: 35,\n      constitution: 150,\n      cultivation: '金丹期'\n    },\n    effects: ['凤凰庇护：受到致命伤害时有20%几率免疫', '火焰抗性：减少50%火焰伤害'],\n    materials: ['凤羽', '秘银', '火晶石'],\n    description: '凤凰羽毛编织而成的护甲，轻盈而坚固',\n    rarity: 4,\n    value: 50000\n  }];\n  useEffect(() => {\n    loadEquipment();\n  }, [projectId]);\n  const loadEquipment = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setEquipment(mockEquipment);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载装备体系失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingEquipment(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = item => {\n    var _item$effects, _item$materials, _item$attributes, _item$attributes2, _item$attributes3, _item$attributes4, _item$attributes5, _item$requirements, _item$requirements2, _item$requirements3, _item$requirements4;\n    setEditingEquipment(item);\n    form.setFieldsValue({\n      ...item,\n      effects: (_item$effects = item.effects) === null || _item$effects === void 0 ? void 0 : _item$effects.join('\\n'),\n      materials: (_item$materials = item.materials) === null || _item$materials === void 0 ? void 0 : _item$materials.join(', '),\n      attack: (_item$attributes = item.attributes) === null || _item$attributes === void 0 ? void 0 : _item$attributes.attack,\n      defense: (_item$attributes2 = item.attributes) === null || _item$attributes2 === void 0 ? void 0 : _item$attributes2.defense,\n      durability: (_item$attributes3 = item.attributes) === null || _item$attributes3 === void 0 ? void 0 : _item$attributes3.durability,\n      criticalRate: (_item$attributes4 = item.attributes) === null || _item$attributes4 === void 0 ? void 0 : _item$attributes4.criticalRate,\n      magicResist: (_item$attributes5 = item.attributes) === null || _item$attributes5 === void 0 ? void 0 : _item$attributes5.magicResist,\n      reqLevel: (_item$requirements = item.requirements) === null || _item$requirements === void 0 ? void 0 : _item$requirements.level,\n      reqStrength: (_item$requirements2 = item.requirements) === null || _item$requirements2 === void 0 ? void 0 : _item$requirements2.strength,\n      reqConstitution: (_item$requirements3 = item.requirements) === null || _item$requirements3 === void 0 ? void 0 : _item$requirements3.constitution,\n      reqCultivation: (_item$requirements4 = item.requirements) === null || _item$requirements4 === void 0 ? void 0 : _item$requirements4.cultivation\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 模拟API调用\n      setEquipment(equipment.filter(e => e.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$effects, _values$materials;\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        category: values.category,\n        grade: values.grade,\n        level: values.level,\n        attributes: {\n          attack: values.attack || 0,\n          defense: values.defense || 0,\n          durability: values.durability || 0,\n          criticalRate: values.criticalRate || 0,\n          magicResist: values.magicResist || 0\n        },\n        requirements: {\n          level: values.reqLevel || 0,\n          strength: values.reqStrength || 0,\n          constitution: values.reqConstitution || 0,\n          cultivation: values.reqCultivation || ''\n        },\n        effects: ((_values$effects = values.effects) === null || _values$effects === void 0 ? void 0 : _values$effects.split('\\n').filter(e => e.trim())) || [],\n        materials: ((_values$materials = values.materials) === null || _values$materials === void 0 ? void 0 : _values$materials.split(',').map(m => m.trim()).filter(m => m)) || [],\n        description: values.description,\n        rarity: values.rarity,\n        value: values.value\n      };\n      if (editingEquipment) {\n        // 更新\n        setEquipment(equipment.map(e => e.id === editingEquipment.id ? {\n          ...e,\n          ...processedValues\n        } : e));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newEquipment = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setEquipment([...equipment, newEquipment]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      weapon: 'red',\n      armor: 'blue',\n      accessory: 'purple',\n      consumable: 'green'\n    };\n    return colors[type] || 'default';\n  };\n  const getGradeColor = grade => {\n    const colors = {\n      common: 'default',\n      uncommon: 'blue',\n      rare: 'purple',\n      epic: 'orange',\n      legendary: 'red',\n      mythic: 'gold'\n    };\n    return colors[grade] || 'default';\n  };\n  const columns = [{\n    title: '装备名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'weapon' ? '武器' : record.type === 'armor' ? '防具' : record.type === 'accessory' ? '饰品' : '消耗品'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '品级',\n    dataIndex: 'grade',\n    key: 'grade',\n    render: grade => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getGradeColor(grade),\n      children: grade === 'common' ? '普通' : grade === 'uncommon' ? '优秀' : grade === 'rare' ? '稀有' : grade === 'epic' ? '史诗' : grade === 'legendary' ? '传说' : '神话'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '等级',\n    dataIndex: 'level',\n    key: 'level',\n    sorter: (a, b) => a.level - b.level\n  }, {\n    title: '稀有度',\n    dataIndex: 'rarity',\n    key: 'rarity',\n    render: rarity => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: rarity,\n      style: {\n        fontSize: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.rarity - b.rarity\n  }, {\n    title: '主属性',\n    key: 'mainAttribute',\n    render: (_, record) => {\n      if (record.type === 'weapon') {\n        var _record$attributes;\n        return /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {\n            style: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: ((_record$attributes = record.attributes) === null || _record$attributes === void 0 ? void 0 : _record$attributes.attack) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this);\n      } else if (record.type === 'armor') {\n        var _record$attributes2;\n        return /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(SafetyOutlined, {\n            style: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: ((_record$attributes2 = record.attributes) === null || _record$attributes2 === void 0 ? void 0 : _record$attributes2.defense) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this);\n      }\n      return '-';\n    }\n  }, {\n    title: '价值',\n    dataIndex: 'value',\n    key: 'value',\n    render: value => `${(value === null || value === void 0 ? void 0 : value.toLocaleString()) || 0} 金币`,\n    sorter: (a, b) => a.value - b.value\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u88C5\\u5907\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 9\n    }, this)\n  }];\n  const filteredEquipment = equipment.filter(item => {\n    if (activeTab === 'all') return true;\n    return item.type === activeTab;\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), \" \\u88C5\\u5907\\u4F53\\u7CFB\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u88C5\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u88C5\\u5907\\u603B\\u6570\",\n            value: equipment.length,\n            prefix: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6B66\\u5668\\u6570\\u91CF\",\n            value: equipment.filter(e => e.type === 'weapon').length,\n            prefix: /*#__PURE__*/_jsxDEV(SwordOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9632\\u5177\\u6570\\u91CF\",\n            value: equipment.filter(e => e.type === 'armor').length,\n            prefix: /*#__PURE__*/_jsxDEV(ShieldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4F20\\u8BF4\\u88C5\\u5907\",\n            value: equipment.filter(e => e.grade === 'legendary').length,\n            prefix: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u5168\\u90E8\"\n        }, \"all\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6B66\\u5668\"\n        }, \"weapon\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u9632\\u5177\"\n        }, \"armor\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u9970\\u54C1\"\n        }, \"accessory\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u6D88\\u8017\\u54C1\"\n        }, \"consumable\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredEquipment,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 件装备`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingEquipment ? '编辑装备' : '添加装备',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 900,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'weapon',\n          category: 'sword',\n          grade: 'common',\n          level: 1,\n          rarity: 1,\n          value: 100\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u88C5\\u5907\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入装备名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u88C5\\u5907\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u88C5\\u5907\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择装备类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"weapon\",\n                  children: \"\\u6B66\\u5668\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"armor\",\n                  children: \"\\u9632\\u5177\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"accessory\",\n                  children: \"\\u9970\\u54C1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"consumable\",\n                  children: \"\\u6D88\\u8017\\u54C1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u88C5\\u5907\\u5206\\u7C7B\",\n              rules: [{\n                required: true,\n                message: '请输入装备分类'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u5251\\u3001\\u76FE\\u3001\\u6212\\u6307\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"grade\",\n              label: \"\\u54C1\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择品级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"common\",\n                  children: \"\\u666E\\u901A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"uncommon\",\n                  children: \"\\u4F18\\u79C0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"rare\",\n                  children: \"\\u7A00\\u6709\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"epic\",\n                  children: \"\\u53F2\\u8BD7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"legendary\",\n                  children: \"\\u4F20\\u8BF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"mythic\",\n                  children: \"\\u795E\\u8BDD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"\\u88C5\\u5907\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请输入装备等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 100,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"rarity\",\n              label: \"\\u7A00\\u6709\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择稀有度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"attack\",\n              label: \"\\u653B\\u51FB\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"defense\",\n              label: \"\\u9632\\u5FA1\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"durability\",\n              label: \"\\u8010\\u4E45\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"criticalRate\",\n              label: \"\\u66B4\\u51FB\\u7387(%)\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                max: 100,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"magicResist\",\n              label: \"\\u9B54\\u6297(%)\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                max: 100,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"value\",\n              label: \"\\u4EF7\\u503C(\\u91D1\\u5E01)\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"reqLevel\",\n              label: \"\\u9700\\u6C42\\u7B49\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"reqStrength\",\n              label: \"\\u9700\\u6C42\\u529B\\u91CF\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"reqConstitution\",\n              label: \"\\u9700\\u6C42\\u4F53\\u8D28\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"reqCultivation\",\n          label: \"\\u9700\\u6C42\\u4FEE\\u4E3A\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u91D1\\u4E39\\u671F\\u3001\\u5143\\u5A74\\u671F\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"materials\",\n          label: \"\\u5236\\u4F5C\\u6750\\u6599\",\n          extra: \"\\u591A\\u4E2A\\u6750\\u6599\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u9F99\\u9CDE, \\u7384\\u94C1, \\u7075\\u77F3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"effects\",\n          label: \"\\u7279\\u6B8A\\u6548\\u679C\",\n          extra: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u6548\\u679C\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u5982\\uFF1A\\u9F99\\u5A01\\uFF1A\\u653B\\u51FB\\u65F6\\u670910%\\u51E0\\u7387\\u9707\\u6151\\u654C\\u4EBA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u88C5\\u5907\\u7684\\u5916\\u89C2\\u3001\\u5386\\u53F2\\u80CC\\u666F\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(EquipmentSystems, \"gtjhhjfSw8lMeiaEGegAlopYkUo=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = EquipmentSystems;\nexport default EquipmentSystems;\nvar _c;\n$RefreshReg$(_c, \"EquipmentSystems\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Rate", "Statistic", "Progress", "Tabs", "PlusOutlined", "EditOutlined", "DeleteOutlined", "ShoppingOutlined", "ThunderboltOutlined", "SafetyOutlined", "CrownOutlined", "StarOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "TabPane", "EquipmentSystems", "_s", "id", "projectId", "equipment", "setEquipment", "loading", "setLoading", "modalVisible", "setModalVisible", "editingEquipment", "setEditingEquipment", "activeTab", "setActiveTab", "form", "useForm", "mockEquipment", "name", "type", "category", "grade", "level", "attributes", "attack", "durability", "criticalRate", "requirements", "strength", "cultivation", "effects", "materials", "description", "rarity", "value", "defense", "magicResist", "constitution", "loadEquipment", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "item", "_item$effects", "_item$materials", "_item$attributes", "_item$attributes2", "_item$attributes3", "_item$attributes4", "_item$attributes5", "_item$requirements", "_item$requirements2", "_item$requirements3", "_item$requirements4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "reqLevel", "reqStrength", "reqConstitution", "reqCultivation", "handleDelete", "filter", "e", "success", "handleSubmit", "values", "_values$effects", "_values$materials", "processedValues", "split", "trim", "map", "m", "newEquipment", "Date", "now", "getTypeColor", "colors", "weapon", "armor", "accessory", "consumable", "getGradeColor", "common", "uncommon", "rare", "epic", "legendary", "mythic", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "sorter", "a", "b", "disabled", "style", "fontSize", "_", "_record$attributes", "_record$attributes2", "toLocaleString", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "filteredEquipment", "className", "gutter", "marginBottom", "span", "size", "length", "prefix", "SwordOutlined", "valueStyle", "ShieldOutlined", "active<PERSON><PERSON>", "onChange", "tab", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "max", "extra", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/EquipmentSystems.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Rate,\n  Statistic,\n  Progress,\n  Tabs\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ShoppingOutlined,\n  ThunderboltOutlined,\n  SafetyOutlined,\n  CrownOutlined,\n  StarOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\n\nconst EquipmentSystems = () => {\n  const { id: projectId } = useParams();\n  const [equipment, setEquipment] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingEquipment, setEditingEquipment] = useState(null);\n  const [activeTab, setActiveTab] = useState('weapon');\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockEquipment = [\n    {\n      id: 1,\n      name: '龙鳞剑',\n      type: 'weapon',\n      category: 'sword',\n      grade: 'legendary',\n      level: 50,\n      attributes: {\n        attack: 1200,\n        durability: 800,\n        criticalRate: 15\n      },\n      requirements: {\n        level: 45,\n        strength: 200,\n        cultivation: '元婴期'\n      },\n      effects: ['龙威：攻击时有10%几率震慑敌人', '破甲：无视30%防御'],\n      materials: ['龙鳞', '玄铁', '灵石'],\n      description: '传说中的神兵，蕴含真龙之力',\n      rarity: 5,\n      value: 100000\n    },\n    {\n      id: 2,\n      name: '凤羽护甲',\n      type: 'armor',\n      category: 'chest',\n      grade: 'epic',\n      level: 40,\n      attributes: {\n        defense: 800,\n        durability: 600,\n        magicResist: 25\n      },\n      requirements: {\n        level: 35,\n        constitution: 150,\n        cultivation: '金丹期'\n      },\n      effects: ['凤凰庇护：受到致命伤害时有20%几率免疫', '火焰抗性：减少50%火焰伤害'],\n      materials: ['凤羽', '秘银', '火晶石'],\n      description: '凤凰羽毛编织而成的护甲，轻盈而坚固',\n      rarity: 4,\n      value: 50000\n    }\n  ];\n\n  useEffect(() => {\n    loadEquipment();\n  }, [projectId]);\n\n  const loadEquipment = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setEquipment(mockEquipment);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载装备体系失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingEquipment(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (item) => {\n    setEditingEquipment(item);\n    form.setFieldsValue({\n      ...item,\n      effects: item.effects?.join('\\n'),\n      materials: item.materials?.join(', '),\n      attack: item.attributes?.attack,\n      defense: item.attributes?.defense,\n      durability: item.attributes?.durability,\n      criticalRate: item.attributes?.criticalRate,\n      magicResist: item.attributes?.magicResist,\n      reqLevel: item.requirements?.level,\n      reqStrength: item.requirements?.strength,\n      reqConstitution: item.requirements?.constitution,\n      reqCultivation: item.requirements?.cultivation\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 模拟API调用\n      setEquipment(equipment.filter(e => e.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        category: values.category,\n        grade: values.grade,\n        level: values.level,\n        attributes: {\n          attack: values.attack || 0,\n          defense: values.defense || 0,\n          durability: values.durability || 0,\n          criticalRate: values.criticalRate || 0,\n          magicResist: values.magicResist || 0\n        },\n        requirements: {\n          level: values.reqLevel || 0,\n          strength: values.reqStrength || 0,\n          constitution: values.reqConstitution || 0,\n          cultivation: values.reqCultivation || ''\n        },\n        effects: values.effects?.split('\\n').filter(e => e.trim()) || [],\n        materials: values.materials?.split(',').map(m => m.trim()).filter(m => m) || [],\n        description: values.description,\n        rarity: values.rarity,\n        value: values.value\n      };\n\n      if (editingEquipment) {\n        // 更新\n        setEquipment(equipment.map(e =>\n          e.id === editingEquipment.id ? { ...e, ...processedValues } : e\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newEquipment = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setEquipment([...equipment, newEquipment]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      weapon: 'red',\n      armor: 'blue',\n      accessory: 'purple',\n      consumable: 'green'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getGradeColor = (grade) => {\n    const colors = {\n      common: 'default',\n      uncommon: 'blue',\n      rare: 'purple',\n      epic: 'orange',\n      legendary: 'red',\n      mythic: 'gold'\n    };\n    return colors[grade] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '装备名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'weapon' ? '武器' :\n             record.type === 'armor' ? '防具' :\n             record.type === 'accessory' ? '饰品' : '消耗品'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '品级',\n      dataIndex: 'grade',\n      key: 'grade',\n      render: (grade) => (\n        <Tag color={getGradeColor(grade)}>\n          {grade === 'common' ? '普通' :\n           grade === 'uncommon' ? '优秀' :\n           grade === 'rare' ? '稀有' :\n           grade === 'epic' ? '史诗' :\n           grade === 'legendary' ? '传说' : '神话'}\n        </Tag>\n      )\n    },\n    {\n      title: '等级',\n      dataIndex: 'level',\n      key: 'level',\n      sorter: (a, b) => a.level - b.level\n    },\n    {\n      title: '稀有度',\n      dataIndex: 'rarity',\n      key: 'rarity',\n      render: (rarity) => (\n        <Rate disabled value={rarity} style={{ fontSize: 16 }} />\n      ),\n      sorter: (a, b) => a.rarity - b.rarity\n    },\n    {\n      title: '主属性',\n      key: 'mainAttribute',\n      render: (_, record) => {\n        if (record.type === 'weapon') {\n          return (\n            <Space>\n              <ThunderboltOutlined style={{ color: '#f5222d' }} />\n              <Text>{record.attributes?.attack || 0}</Text>\n            </Space>\n          );\n        } else if (record.type === 'armor') {\n          return (\n            <Space>\n              <SafetyOutlined style={{ color: '#1890ff' }} />\n              <Text>{record.attributes?.defense || 0}</Text>\n            </Space>\n          );\n        }\n        return '-';\n      }\n    },\n    {\n      title: '价值',\n      dataIndex: 'value',\n      key: 'value',\n      render: (value) => `${value?.toLocaleString() || 0} 金币`,\n      sorter: (a, b) => a.value - b.value\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个装备吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  const filteredEquipment = equipment.filter(item => {\n    if (activeTab === 'all') return true;\n    return item.type === activeTab;\n  });\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <ShoppingOutlined /> 装备体系管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加装备\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"装备总数\"\n              value={equipment.length}\n              prefix={<ShoppingOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"武器数量\"\n              value={equipment.filter(e => e.type === 'weapon').length}\n              prefix={<SwordOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"防具数量\"\n              value={equipment.filter(e => e.type === 'armor').length}\n              prefix={<ShieldOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"传说装备\"\n              value={equipment.filter(e => e.grade === 'legendary').length}\n              prefix={<CrownOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Tabs activeKey={activeTab} onChange={setActiveTab}>\n          <TabPane tab=\"全部\" key=\"all\" />\n          <TabPane tab=\"武器\" key=\"weapon\" />\n          <TabPane tab=\"防具\" key=\"armor\" />\n          <TabPane tab=\"饰品\" key=\"accessory\" />\n          <TabPane tab=\"消耗品\" key=\"consumable\" />\n        </Tabs>\n\n        <Table\n          columns={columns}\n          dataSource={filteredEquipment}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 件装备`\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingEquipment ? '编辑装备' : '添加装备'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={900}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'weapon',\n            category: 'sword',\n            grade: 'common',\n            level: 1,\n            rarity: 1,\n            value: 100\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"name\"\n                label=\"装备名称\"\n                rules={[{ required: true, message: '请输入装备名称' }]}\n              >\n                <Input placeholder=\"请输入装备名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"装备类型\"\n                rules={[{ required: true, message: '请选择装备类型' }]}\n              >\n                <Select>\n                  <Option value=\"weapon\">武器</Option>\n                  <Option value=\"armor\">防具</Option>\n                  <Option value=\"accessory\">饰品</Option>\n                  <Option value=\"consumable\">消耗品</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"装备分类\"\n                rules={[{ required: true, message: '请输入装备分类' }]}\n              >\n                <Input placeholder=\"如：剑、盾、戒指等\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"grade\"\n                label=\"品级\"\n                rules={[{ required: true, message: '请选择品级' }]}\n              >\n                <Select>\n                  <Option value=\"common\">普通</Option>\n                  <Option value=\"uncommon\">优秀</Option>\n                  <Option value=\"rare\">稀有</Option>\n                  <Option value=\"epic\">史诗</Option>\n                  <Option value=\"legendary\">传说</Option>\n                  <Option value=\"mythic\">神话</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"装备等级\"\n                rules={[{ required: true, message: '请输入装备等级' }]}\n              >\n                <InputNumber min={1} max={100} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"rarity\"\n                label=\"稀有度\"\n                rules={[{ required: true, message: '请选择稀有度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item name=\"attack\" label=\"攻击力\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"defense\" label=\"防御力\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"durability\" label=\"耐久度\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item name=\"criticalRate\" label=\"暴击率(%)\">\n                <InputNumber min={0} max={100} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"magicResist\" label=\"魔抗(%)\">\n                <InputNumber min={0} max={100} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"value\" label=\"价值(金币)\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item name=\"reqLevel\" label=\"需求等级\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"reqStrength\" label=\"需求力量\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"reqConstitution\" label=\"需求体质\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"reqCultivation\" label=\"需求修为\">\n            <Input placeholder=\"如：金丹期、元婴期等\" />\n          </Form.Item>\n\n          <Form.Item name=\"materials\" label=\"制作材料\" extra=\"多个材料请用逗号分隔\">\n            <Input placeholder=\"如：龙鳞, 玄铁, 灵石\" />\n          </Form.Item>\n\n          <Form.Item name=\"effects\" label=\"特殊效果\" extra=\"每行一个效果\">\n            <TextArea rows={3} placeholder=\"如：龙威：攻击时有10%几率震慑敌人\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <TextArea rows={3} placeholder=\"请描述装备的外观、历史背景等\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default EquipmentSystems;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,gBAAgB,EAChBC,mBAAmB,EACnBC,cAAc,EACdC,aAAa,EACbC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAClC,MAAM;EAAEuB;AAAS,CAAC,GAAG3B,KAAK;AAC1B,MAAM;EAAE4B;AAAO,CAAC,GAAG3B,MAAM;AACzB,MAAM;EAAE4B;AAAQ,CAAC,GAAGf,IAAI;AAExB,MAAMgB,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGvC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACoD,IAAI,CAAC,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG,CACpB;IACEd,EAAE,EAAE,CAAC;IACLe,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE;MACVC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,GAAG;MACfC,YAAY,EAAE;IAChB,CAAC;IACDC,YAAY,EAAE;MACZL,KAAK,EAAE,EAAE;MACTM,QAAQ,EAAE,GAAG;MACbC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC;IAC3CC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLe,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,OAAO;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE;MACVY,OAAO,EAAE,GAAG;MACZV,UAAU,EAAE,GAAG;MACfW,WAAW,EAAE;IACf,CAAC;IACDT,YAAY,EAAE;MACZL,KAAK,EAAE,EAAE;MACTe,YAAY,EAAE,GAAG;MACjBR,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,CAAC;IACnDC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IAC9BC,WAAW,EAAE,mBAAmB;IAChCC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACT,CAAC,CACF;EAEDtE,SAAS,CAAC,MAAM;IACd0E,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;EAEf,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC9B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA+B,UAAU,CAAC,MAAM;QACfjC,YAAY,CAACW,aAAa,CAAC;QAC3BT,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,UAAU,CAAC;MACzBhC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,SAAS,GAAGA,CAAA,KAAM;IACtB7B,mBAAmB,CAAC,IAAI,CAAC;IACzBG,IAAI,CAAC2B,WAAW,CAAC,CAAC;IAClBhC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiC,UAAU,GAAIC,IAAI,IAAK;IAAA,IAAAC,aAAA,EAAAC,eAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;IAC3B3C,mBAAmB,CAACgC,IAAI,CAAC;IACzB7B,IAAI,CAACyC,cAAc,CAAC;MAClB,GAAGZ,IAAI;MACPd,OAAO,GAAAe,aAAA,GAAED,IAAI,CAACd,OAAO,cAAAe,aAAA,uBAAZA,aAAA,CAAcY,IAAI,CAAC,IAAI,CAAC;MACjC1B,SAAS,GAAAe,eAAA,GAAEF,IAAI,CAACb,SAAS,cAAAe,eAAA,uBAAdA,eAAA,CAAgBW,IAAI,CAAC,IAAI,CAAC;MACrCjC,MAAM,GAAAuB,gBAAA,GAAEH,IAAI,CAACrB,UAAU,cAAAwB,gBAAA,uBAAfA,gBAAA,CAAiBvB,MAAM;MAC/BW,OAAO,GAAAa,iBAAA,GAAEJ,IAAI,CAACrB,UAAU,cAAAyB,iBAAA,uBAAfA,iBAAA,CAAiBb,OAAO;MACjCV,UAAU,GAAAwB,iBAAA,GAAEL,IAAI,CAACrB,UAAU,cAAA0B,iBAAA,uBAAfA,iBAAA,CAAiBxB,UAAU;MACvCC,YAAY,GAAAwB,iBAAA,GAAEN,IAAI,CAACrB,UAAU,cAAA2B,iBAAA,uBAAfA,iBAAA,CAAiBxB,YAAY;MAC3CU,WAAW,GAAAe,iBAAA,GAAEP,IAAI,CAACrB,UAAU,cAAA4B,iBAAA,uBAAfA,iBAAA,CAAiBf,WAAW;MACzCsB,QAAQ,GAAAN,kBAAA,GAAER,IAAI,CAACjB,YAAY,cAAAyB,kBAAA,uBAAjBA,kBAAA,CAAmB9B,KAAK;MAClCqC,WAAW,GAAAN,mBAAA,GAAET,IAAI,CAACjB,YAAY,cAAA0B,mBAAA,uBAAjBA,mBAAA,CAAmBzB,QAAQ;MACxCgC,eAAe,GAAAN,mBAAA,GAAEV,IAAI,CAACjB,YAAY,cAAA2B,mBAAA,uBAAjBA,mBAAA,CAAmBjB,YAAY;MAChDwB,cAAc,GAAAN,mBAAA,GAAEX,IAAI,CAACjB,YAAY,cAAA4B,mBAAA,uBAAjBA,mBAAA,CAAmB1B;IACrC,CAAC,CAAC;IACFnB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoD,YAAY,GAAG,MAAO3D,EAAE,IAAK;IACjC,IAAI;MACF;MACAG,YAAY,CAACD,SAAS,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAKA,EAAE,CAAC,CAAC;MAChDvB,OAAO,CAACqF,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAM0B,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MAAA,IAAAC,eAAA,EAAAC,iBAAA;MACF,MAAMC,eAAe,GAAG;QACtBpD,IAAI,EAAEiD,MAAM,CAACjD,IAAI;QACjBC,IAAI,EAAEgD,MAAM,CAAChD,IAAI;QACjBC,QAAQ,EAAE+C,MAAM,CAAC/C,QAAQ;QACzBC,KAAK,EAAE8C,MAAM,CAAC9C,KAAK;QACnBC,KAAK,EAAE6C,MAAM,CAAC7C,KAAK;QACnBC,UAAU,EAAE;UACVC,MAAM,EAAE2C,MAAM,CAAC3C,MAAM,IAAI,CAAC;UAC1BW,OAAO,EAAEgC,MAAM,CAAChC,OAAO,IAAI,CAAC;UAC5BV,UAAU,EAAE0C,MAAM,CAAC1C,UAAU,IAAI,CAAC;UAClCC,YAAY,EAAEyC,MAAM,CAACzC,YAAY,IAAI,CAAC;UACtCU,WAAW,EAAE+B,MAAM,CAAC/B,WAAW,IAAI;QACrC,CAAC;QACDT,YAAY,EAAE;UACZL,KAAK,EAAE6C,MAAM,CAACT,QAAQ,IAAI,CAAC;UAC3B9B,QAAQ,EAAEuC,MAAM,CAACR,WAAW,IAAI,CAAC;UACjCtB,YAAY,EAAE8B,MAAM,CAACP,eAAe,IAAI,CAAC;UACzC/B,WAAW,EAAEsC,MAAM,CAACN,cAAc,IAAI;QACxC,CAAC;QACD/B,OAAO,EAAE,EAAAsC,eAAA,GAAAD,MAAM,CAACrC,OAAO,cAAAsC,eAAA,uBAAdA,eAAA,CAAgBG,KAAK,CAAC,IAAI,CAAC,CAACR,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;QAChEzC,SAAS,EAAE,EAAAsC,iBAAA,GAAAF,MAAM,CAACpC,SAAS,cAAAsC,iBAAA,uBAAhBA,iBAAA,CAAkBE,KAAK,CAAC,GAAG,CAAC,CAACE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACT,MAAM,CAACW,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QAC/E1C,WAAW,EAAEmC,MAAM,CAACnC,WAAW;QAC/BC,MAAM,EAAEkC,MAAM,CAAClC,MAAM;QACrBC,KAAK,EAAEiC,MAAM,CAACjC;MAChB,CAAC;MAED,IAAIvB,gBAAgB,EAAE;QACpB;QACAL,YAAY,CAACD,SAAS,CAACoE,GAAG,CAACT,CAAC,IAC1BA,CAAC,CAAC7D,EAAE,KAAKQ,gBAAgB,CAACR,EAAE,GAAG;UAAE,GAAG6D,CAAC;UAAE,GAAGM;QAAgB,CAAC,GAAGN,CAChE,CAAC,CAAC;QACFpF,OAAO,CAACqF,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMU,YAAY,GAAG;UACnBxE,EAAE,EAAEyE,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGP;QACL,CAAC;QACDhE,YAAY,CAAC,CAAC,GAAGD,SAAS,EAAEsE,YAAY,CAAC,CAAC;QAC1C/F,OAAO,CAACqF,OAAO,CAAC,MAAM,CAAC;MACzB;MACAvD,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMsC,YAAY,GAAI3D,IAAI,IAAK;IAC7B,MAAM4D,MAAM,GAAG;MACbC,MAAM,EAAE,KAAK;MACbC,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,QAAQ;MACnBC,UAAU,EAAE;IACd,CAAC;IACD,OAAOJ,MAAM,CAAC5D,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAMiE,aAAa,GAAI/D,KAAK,IAAK;IAC/B,MAAM0D,MAAM,GAAG;MACbM,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,KAAK;MAChBC,MAAM,EAAE;IACV,CAAC;IACD,OAAOX,MAAM,CAAC1D,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,MAAMsE,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnBtG,OAAA,CAACrB,KAAK;MAAA4H,QAAA,gBACJvG,OAAA,CAACE,IAAI;QAACsG,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1B5G,OAAA,CAACjB,GAAG;QAAC8H,KAAK,EAAE1B,YAAY,CAACmB,MAAM,CAAC9E,IAAI,CAAE;QAAA+E,QAAA,EACnCD,MAAM,CAAC9E,IAAI,KAAK,QAAQ,GAAG,IAAI,GAC/B8E,MAAM,CAAC9E,IAAI,KAAK,OAAO,GAAG,IAAI,GAC9B8E,MAAM,CAAC9E,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG;MAAK;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAG1E,KAAK,iBACZ1B,OAAA,CAACjB,GAAG;MAAC8H,KAAK,EAAEpB,aAAa,CAAC/D,KAAK,CAAE;MAAA6E,QAAA,EAC9B7E,KAAK,KAAK,QAAQ,GAAG,IAAI,GACzBA,KAAK,KAAK,UAAU,GAAG,IAAI,GAC3BA,KAAK,KAAK,MAAM,GAAG,IAAI,GACvBA,KAAK,KAAK,MAAM,GAAG,IAAI,GACvBA,KAAK,KAAK,WAAW,GAAG,IAAI,GAAG;IAAI;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZW,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACpF,KAAK,GAAGqF,CAAC,CAACrF;EAChC,CAAC,EACD;IACEsE,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG9D,MAAM,iBACbtC,OAAA,CAACb,IAAI;MAAC8H,QAAQ;MAAC1E,KAAK,EAAED,MAAO;MAAC4E,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACzD;IACDE,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzE,MAAM,GAAG0E,CAAC,CAAC1E;EACjC,CAAC,EACD;IACE2D,KAAK,EAAE,KAAK;IACZE,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACgB,CAAC,EAAEd,MAAM,KAAK;MACrB,IAAIA,MAAM,CAAC9E,IAAI,KAAK,QAAQ,EAAE;QAAA,IAAA6F,kBAAA;QAC5B,oBACErH,OAAA,CAACrB,KAAK;UAAA4H,QAAA,gBACJvG,OAAA,CAACL,mBAAmB;YAACuH,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD5G,OAAA,CAACE,IAAI;YAAAqG,QAAA,EAAE,EAAAc,kBAAA,GAAAf,MAAM,CAAC1E,UAAU,cAAAyF,kBAAA,uBAAjBA,kBAAA,CAAmBxF,MAAM,KAAI;UAAC;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAEZ,CAAC,MAAM,IAAIN,MAAM,CAAC9E,IAAI,KAAK,OAAO,EAAE;QAAA,IAAA8F,mBAAA;QAClC,oBACEtH,OAAA,CAACrB,KAAK;UAAA4H,QAAA,gBACJvG,OAAA,CAACJ,cAAc;YAACsH,KAAK,EAAE;cAAEL,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C5G,OAAA,CAACE,IAAI;YAAAqG,QAAA,EAAE,EAAAe,mBAAA,GAAAhB,MAAM,CAAC1E,UAAU,cAAA0F,mBAAA,uBAAjBA,mBAAA,CAAmB9E,OAAO,KAAI;UAAC;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAEZ;MACA,OAAO,GAAG;IACZ;EACF,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAG7D,KAAK,IAAK,GAAG,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgF,cAAc,CAAC,CAAC,KAAI,CAAC,KAAK;IACvDT,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxE,KAAK,GAAGyE,CAAC,CAACzE;EAChC,CAAC,EACD;IACE0D,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACgB,CAAC,EAAEd,MAAM,kBAChBtG,OAAA,CAACrB,KAAK;MAAA4H,QAAA,gBACJvG,OAAA,CAACd,OAAO;QAAC+G,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjBvG,OAAA,CAAC3B,MAAM;UACLmD,IAAI,EAAC,MAAM;UACXgG,IAAI,eAAExH,OAAA,CAACR,YAAY;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAMzE,UAAU,CAACsD,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV5G,OAAA,CAAChB,UAAU;QACTiH,KAAK,EAAC,8DAAY;QAClByB,SAAS,EAAEA,CAAA,KAAMvD,YAAY,CAACmC,MAAM,CAAC9F,EAAE,CAAE;QACzCmH,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAArB,QAAA,eAEfvG,OAAA,CAACd,OAAO;UAAC+G,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjBvG,OAAA,CAAC3B,MAAM;YACLmD,IAAI,EAAC,MAAM;YACXqG,MAAM;YACNL,IAAI,eAAExH,OAAA,CAACP,cAAc;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,MAAMkB,iBAAiB,GAAGpH,SAAS,CAAC0D,MAAM,CAACnB,IAAI,IAAI;IACjD,IAAI/B,SAAS,KAAK,KAAK,EAAE,OAAO,IAAI;IACpC,OAAO+B,IAAI,CAACzB,IAAI,KAAKN,SAAS;EAChC,CAAC,CAAC;EAEF,oBACElB,OAAA;IAAK+H,SAAS,EAAC,SAAS;IAAAxB,QAAA,gBACtBvG,OAAA;MAAK+H,SAAS,EAAC,aAAa;MAAAxB,QAAA,gBAC1BvG,OAAA,CAACC,KAAK;QAAC0B,KAAK,EAAE,CAAE;QAACoG,SAAS,EAAC,YAAY;QAAAxB,QAAA,gBACrCvG,OAAA,CAACN,gBAAgB;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCACtB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5G,OAAA,CAAC3B,MAAM;QACLmD,IAAI,EAAC,SAAS;QACdgG,IAAI,eAAExH,OAAA,CAACT,YAAY;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAE3E,SAAU;QAAAyD,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN5G,OAAA,CAACnB,GAAG;MAACmJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACd,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAG,CAAE;MAAA1B,QAAA,gBACjDvG,OAAA,CAAClB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXvG,OAAA,CAAC7B,IAAI;UAACgK,IAAI,EAAC,OAAO;UAAA5B,QAAA,eAChBvG,OAAA,CAACZ,SAAS;YACR6G,KAAK,EAAC,0BAAM;YACZ1D,KAAK,EAAE7B,SAAS,CAAC0H,MAAO;YACxBC,MAAM,eAAErI,OAAA,CAACN,gBAAgB;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5G,OAAA,CAAClB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXvG,OAAA,CAAC7B,IAAI;UAACgK,IAAI,EAAC,OAAO;UAAA5B,QAAA,eAChBvG,OAAA,CAACZ,SAAS;YACR6G,KAAK,EAAC,0BAAM;YACZ1D,KAAK,EAAE7B,SAAS,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,IAAI,KAAK,QAAQ,CAAC,CAAC4G,MAAO;YACzDC,MAAM,eAAErI,OAAA,CAACsI,aAAa;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1B2B,UAAU,EAAE;cAAE1B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5G,OAAA,CAAClB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXvG,OAAA,CAAC7B,IAAI;UAACgK,IAAI,EAAC,OAAO;UAAA5B,QAAA,eAChBvG,OAAA,CAACZ,SAAS;YACR6G,KAAK,EAAC,0BAAM;YACZ1D,KAAK,EAAE7B,SAAS,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7C,IAAI,KAAK,OAAO,CAAC,CAAC4G,MAAO;YACxDC,MAAM,eAAErI,OAAA,CAACwI,cAAc;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3B2B,UAAU,EAAE;cAAE1B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN5G,OAAA,CAAClB,GAAG;QAACoJ,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXvG,OAAA,CAAC7B,IAAI;UAACgK,IAAI,EAAC,OAAO;UAAA5B,QAAA,eAChBvG,OAAA,CAACZ,SAAS;YACR6G,KAAK,EAAC,0BAAM;YACZ1D,KAAK,EAAE7B,SAAS,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3C,KAAK,KAAK,WAAW,CAAC,CAAC0G,MAAO;YAC7DC,MAAM,eAAErI,OAAA,CAACH,aAAa;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1B2B,UAAU,EAAE;cAAE1B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5G,OAAA,CAAC7B,IAAI;MAAAoI,QAAA,gBACHvG,OAAA,CAACV,IAAI;QAACmJ,SAAS,EAAEvH,SAAU;QAACwH,QAAQ,EAAEvH,YAAa;QAAAoF,QAAA,gBACjDvG,OAAA,CAACK,OAAO;UAACsI,GAAG,EAAC;QAAI,GAAK,KAAK;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B5G,OAAA,CAACK,OAAO;UAACsI,GAAG,EAAC;QAAI,GAAK,QAAQ;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjC5G,OAAA,CAACK,OAAO;UAACsI,GAAG,EAAC;QAAI,GAAK,OAAO;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChC5G,OAAA,CAACK,OAAO;UAACsI,GAAG,EAAC;QAAI,GAAK,WAAW;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpC5G,OAAA,CAACK,OAAO;UAACsI,GAAG,EAAC;QAAK,GAAK,YAAY;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAEP5G,OAAA,CAAC5B,KAAK;QACJ4H,OAAO,EAAEA,OAAQ;QACjB4C,UAAU,EAAEd,iBAAkB;QAC9Be,MAAM,EAAC,IAAI;QACXjI,OAAO,EAAEA,OAAQ;QACjBkI,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEP5G,OAAA,CAAC1B,KAAK;MACJ2H,KAAK,EAAEjF,gBAAgB,GAAG,MAAM,GAAG,MAAO;MAC1CoI,IAAI,EAAEtI,YAAa;MACnBuI,QAAQ,EAAEA,CAAA,KAAMtI,eAAe,CAAC,KAAK,CAAE;MACvCuI,IAAI,EAAEA,CAAA,KAAMlI,IAAI,CAACmI,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAE5I,OAAQ;MACxB6I,KAAK,EAAE,GAAI;MAAAlD,QAAA,eAEXvG,OAAA,CAACzB,IAAI;QACH6C,IAAI,EAAEA,IAAK;QACXsI,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEpF,YAAa;QACvBqF,aAAa,EAAE;UACbpI,IAAI,EAAE,QAAQ;UACdC,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE,QAAQ;UACfC,KAAK,EAAE,CAAC;UACRW,MAAM,EAAE,CAAC;UACTC,KAAK,EAAE;QACT,CAAE;QAAAgE,QAAA,gBAEFvG,OAAA,CAACnB,GAAG;UAACmJ,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdvG,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsH,QAAA,eAEhDvG,OAAA,CAACxB,KAAK;gBAACyL,WAAW,EAAC;cAAS;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsH,QAAA,eAEhDvG,OAAA,CAACvB,MAAM;gBAAA8H,QAAA,gBACLvG,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,QAAQ;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,OAAO;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,WAAW;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,YAAY;kBAAAgE,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,UAAU;cACfuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsH,QAAA,eAEhDvG,OAAA,CAACxB,KAAK;gBAACyL,WAAW,EAAC;cAAW;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA,CAACnB,GAAG;UAACmJ,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdvG,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,OAAO;cACZuI,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAsH,QAAA,eAE9CvG,OAAA,CAACvB,MAAM;gBAAA8H,QAAA,gBACLvG,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,QAAQ;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,UAAU;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,MAAM;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,MAAM;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,WAAW;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrC5G,OAAA,CAACI,MAAM;kBAACmC,KAAK,EAAC,QAAQ;kBAAAgE,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,OAAO;cACZuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsH,QAAA,eAEhDvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,GAAI;gBAACjD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cACRtI,IAAI,EAAC,QAAQ;cACbuI,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/K,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAsH,QAAA,eAE/CvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAACjD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA,CAACnB,GAAG;UAACmJ,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdvG,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,QAAQ;cAACuI,KAAK,EAAC,oBAAK;cAAAvD,QAAA,eAClCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,SAAS;cAACuI,KAAK,EAAC,oBAAK;cAAAvD,QAAA,eACnCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,YAAY;cAACuI,KAAK,EAAC,oBAAK;cAAAvD,QAAA,eACtCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA,CAACnB,GAAG;UAACmJ,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdvG,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,cAAc;cAACuI,KAAK,EAAC,uBAAQ;cAAAvD,QAAA,eAC3CvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,GAAI;gBAACjD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,aAAa;cAACuI,KAAK,EAAC,iBAAO;cAAAvD,QAAA,eACzCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,GAAI;gBAACjD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,OAAO;cAACuI,KAAK,EAAC,4BAAQ;cAAAvD,QAAA,eACpCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA,CAACnB,GAAG;UAACmJ,MAAM,EAAE,EAAG;UAAAzB,QAAA,gBACdvG,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,UAAU;cAACuI,KAAK,EAAC,0BAAM;cAAAvD,QAAA,eACrCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,aAAa;cAACuI,KAAK,EAAC,0BAAM;cAAAvD,QAAA,eACxCvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN5G,OAAA,CAAClB,GAAG;YAACoJ,IAAI,EAAE,CAAE;YAAA3B,QAAA,eACXvG,OAAA,CAACzB,IAAI,CAACsL,IAAI;cAACtI,IAAI,EAAC,iBAAiB;cAACuI,KAAK,EAAC,0BAAM;cAAAvD,QAAA,eAC5CvG,OAAA,CAACtB,WAAW;gBAACwL,GAAG,EAAE,CAAE;gBAAChD,KAAK,EAAE;kBAAEuC,KAAK,EAAE;gBAAO;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN5G,OAAA,CAACzB,IAAI,CAACsL,IAAI;UAACtI,IAAI,EAAC,gBAAgB;UAACuI,KAAK,EAAC,0BAAM;UAAAvD,QAAA,eAC3CvG,OAAA,CAACxB,KAAK;YAACyL,WAAW,EAAC;UAAY;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZ5G,OAAA,CAACzB,IAAI,CAACsL,IAAI;UAACtI,IAAI,EAAC,WAAW;UAACuI,KAAK,EAAC,0BAAM;UAACM,KAAK,EAAC,8DAAY;UAAA7D,QAAA,eACzDvG,OAAA,CAACxB,KAAK;YAACyL,WAAW,EAAC;UAAc;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZ5G,OAAA,CAACzB,IAAI,CAACsL,IAAI;UAACtI,IAAI,EAAC,SAAS;UAACuI,KAAK,EAAC,0BAAM;UAACM,KAAK,EAAC,sCAAQ;UAAA7D,QAAA,eACnDvG,OAAA,CAACG,QAAQ;YAACkK,IAAI,EAAE,CAAE;YAACJ,WAAW,EAAC;UAAoB;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEZ5G,OAAA,CAACzB,IAAI,CAACsL,IAAI;UACRtI,IAAI,EAAC,aAAa;UAClBuI,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/K,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAsH,QAAA,eAE9CvG,OAAA,CAACG,QAAQ;YAACkK,IAAI,EAAE,CAAE;YAACJ,WAAW,EAAC;UAAgB;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrG,EAAA,CA/hBID,gBAAgB;EAAA,QACMpC,SAAS,EAMpBK,IAAI,CAAC8C,OAAO;AAAA;AAAAiJ,EAAA,GAPvBhK,gBAAgB;AAiiBtB,eAAeA,gBAAgB;AAAC,IAAAgK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}