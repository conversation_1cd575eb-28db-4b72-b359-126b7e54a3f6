import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, List, Button, Progress, Tag, Space, Typography } from 'antd';
import {
  ProjectOutlined,
  UserOutlined,
  BookOutlined,
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const Dashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    totalProjects: 5,
    totalCharacters: 23,
    totalVolumes: 12,
    totalWords: 234567
  });

  const [recentProjects, setRecentProjects] = useState([
    {
      id: 1,
      name: '仙侠传说',
      type: 'xianxia',
      status: 'writing',
      progress: 65,
      lastModified: '2024-01-15',
      wordCount: 89000
    },
    {
      id: 2,
      name: '星际征途',
      type: 'scifi',
      status: 'planning',
      progress: 25,
      lastModified: '2024-01-14',
      wordCount: 12000
    },
    {
      id: 3,
      name: '都市修仙',
      type: 'modern',
      status: 'writing',
      progress: 80,
      lastModified: '2024-01-13',
      wordCount: 156000
    }
  ]);

  const [recentActivities, setRecentActivities] = useState([
    {
      id: 1,
      type: 'chapter',
      action: '创建了新章节',
      target: '第十二章：突破',
      project: '仙侠传说',
      time: '2小时前'
    },
    {
      id: 2,
      type: 'character',
      action: '更新了人物',
      target: '李逍遥',
      project: '仙侠传说',
      time: '4小时前'
    },
    {
      id: 3,
      type: 'plot',
      action: '完成了剧情',
      target: '主线剧情：初入江湖',
      project: '仙侠传说',
      time: '1天前'
    },
    {
      id: 4,
      type: 'project',
      action: '创建了新项目',
      target: '星际征途',
      project: '',
      time: '2天前'
    }
  ]);

  const getStatusColor = (status) => {
    const colors = {
      planning: 'blue',
      writing: 'green',
      reviewing: 'orange',
      completed: 'purple',
      published: 'gold'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      planning: '规划中',
      writing: '写作中',
      reviewing: '审阅中',
      completed: '已完成',
      published: '已发布'
    };
    return texts[status] || status;
  };

  const getTypeText = (type) => {
    const texts = {
      fantasy: '奇幻',
      xianxia: '仙侠',
      wuxia: '武侠',
      scifi: '科幻',
      modern: '现代',
      historical: '历史',
      romance: '言情'
    };
    return texts[type] || type;
  };

  const getActivityIcon = (type) => {
    const icons = {
      project: <ProjectOutlined />,
      character: <UserOutlined />,
      chapter: <FileTextOutlined />,
      plot: <BookOutlined />
    };
    return icons[type] || <EditOutlined />;
  };

  return (
    <div className="fade-in">
      <div className="page-header">
        <Title level={2} className="page-title">仪表盘</Title>
        <Text className="page-description">欢迎回来！查看您的创作概况和最近活动。</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-grid">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="项目总数"
              value={stats.totalProjects}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="人物总数"
              value={stats.totalCharacters}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="卷宗总数"
              value={stats.totalVolumes}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总字数"
              value={stats.totalWords}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#fa8c16' }}
              formatter={(value) => `${(value / 10000).toFixed(1)}万`}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        {/* 最近项目 */}
        <Col xs={24} lg={14}>
          <Card
            title="最近项目"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/projects')}
              >
                新建项目
              </Button>
            }
          >
            <List
              dataSource={recentProjects}
              renderItem={(project) => (
                <List.Item
                  actions={[
                    <Button
                      type="link"
                      onClick={() => navigate(`/projects/${project.id}`)}
                    >
                      查看详情
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{project.name}</span>
                        <Tag color={getStatusColor(project.status)}>
                          {getStatusText(project.status)}
                        </Tag>
                        <Tag>{getTypeText(project.type)}</Tag>
                      </Space>
                    }
                    description={
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div>
                          <Text type="secondary">
                            字数: {(project.wordCount / 10000).toFixed(1)}万 |
                            最后修改: {project.lastModified}
                          </Text>
                        </div>
                        <Progress
                          percent={project.progress}
                          size="small"
                          status={project.progress === 100 ? 'success' : 'active'}
                        />
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={10}>
          <Card title="最近活动">
            <List
              dataSource={recentActivities}
              renderItem={(activity) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(activity.type)}
                    title={
                      <Space>
                        <span>{activity.action}</span>
                        <Text strong>{activity.target}</Text>
                      </Space>
                    }
                    description={
                      <Space>
                        {activity.project && (
                          <Text type="secondary">项目: {activity.project}</Text>
                        )}
                        <Text type="secondary">
                          <ClockCircleOutlined /> {activity.time}
                        </Text>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Button
              type="dashed"
              block
              size="large"
              icon={<ProjectOutlined />}
              onClick={() => navigate('/projects')}
            >
              创建新项目
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              type="dashed"
              block
              size="large"
              icon={<UserOutlined />}
              onClick={() => navigate('/characters')}
            >
              添加人物
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              type="dashed"
              block
              size="large"
              icon={<FileTextOutlined />}
              onClick={() => navigate('/volumes')}
            >
              管理卷宗
            </Button>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Button
              type="dashed"
              block
              size="large"
              icon={<BookOutlined />}
              onClick={() => navigate('/ai-assistant')}
            >
              AI助手
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default Dashboard;
