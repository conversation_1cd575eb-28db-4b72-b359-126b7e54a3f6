{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\MapStructure.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Statistic, Tree } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EnvironmentOutlined, GlobalOutlined, HomeOutlined, BankOutlined, ShopOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst MapStructure = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [locations, setLocations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingLocation, setEditingLocation] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockLocations = [{\n    id: 1,\n    name: '天元大陆',\n    type: 'continent',\n    parentId: null,\n    coordinates: {\n      x: 0,\n      y: 0\n    },\n    size: 'massive',\n    climate: 'temperate',\n    terrain: 'mixed',\n    population: ********,\n    dangerLevel: 2,\n    resources: ['灵石', '药材', '矿物'],\n    controllingFaction: '修仙联盟',\n    description: '修仙世界的主大陆，包含多个国家和宗门',\n    specialFeatures: ['灵气浓郁', '天地法则完整'],\n    accessRequirements: '无',\n    status: 'active'\n  }, {\n    id: 2,\n    name: '青云山脉',\n    type: 'mountain',\n    parentId: 1,\n    coordinates: {\n      x: 100,\n      y: 200\n    },\n    size: 'large',\n    climate: 'cold',\n    terrain: 'mountain',\n    population: 50000,\n    dangerLevel: 3,\n    resources: ['灵石矿', '雪莲', '寒冰晶'],\n    controllingFaction: '青云宗',\n    description: '连绵不绝的山脉，青云宗的总部所在地',\n    specialFeatures: ['常年云雾缭绕', '灵气充沛'],\n    accessRequirements: '青云宗弟子或邀请函',\n    status: 'active'\n  }, {\n    id: 3,\n    name: '青云宗',\n    type: 'sect',\n    parentId: 2,\n    coordinates: {\n      x: 120,\n      y: 220\n    },\n    size: 'medium',\n    climate: 'cold',\n    terrain: 'mountain',\n    population: 5000,\n    dangerLevel: 1,\n    resources: ['功法秘籍', '丹药', '法器'],\n    controllingFaction: '青云宗',\n    description: '正道第一大宗门，传承千年',\n    specialFeatures: ['护山大阵', '藏经阁', '炼丹房'],\n    accessRequirements: '宗门弟子或长老许可',\n    status: 'active'\n  }];\n  useEffect(() => {\n    loadLocations();\n  }, [projectId]);\n  const loadLocations = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setLocations(mockLocations);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载地图结构失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingLocation(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = location => {\n    var _location$resources, _location$specialFeat, _location$coordinates, _location$coordinates2;\n    setEditingLocation(location);\n    form.setFieldsValue({\n      ...location,\n      resources: (_location$resources = location.resources) === null || _location$resources === void 0 ? void 0 : _location$resources.join(', '),\n      specialFeatures: (_location$specialFeat = location.specialFeatures) === null || _location$specialFeat === void 0 ? void 0 : _location$specialFeat.join(', '),\n      x: (_location$coordinates = location.coordinates) === null || _location$coordinates === void 0 ? void 0 : _location$coordinates.x,\n      y: (_location$coordinates2 = location.coordinates) === null || _location$coordinates2 === void 0 ? void 0 : _location$coordinates2.y\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 检查是否有子位置\n      const hasChildren = locations.some(loc => loc.parentId === id);\n      if (hasChildren) {\n        message.error('请先删除子位置');\n        return;\n      }\n      setLocations(locations.filter(l => l.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$resources, _values$specialFeatur;\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        parentId: values.parentId || null,\n        coordinates: {\n          x: values.x || 0,\n          y: values.y || 0\n        },\n        size: values.size,\n        climate: values.climate,\n        terrain: values.terrain,\n        population: values.population,\n        dangerLevel: values.dangerLevel,\n        resources: ((_values$resources = values.resources) === null || _values$resources === void 0 ? void 0 : _values$resources.split(',').map(r => r.trim()).filter(r => r)) || [],\n        controllingFaction: values.controllingFaction,\n        description: values.description,\n        specialFeatures: ((_values$specialFeatur = values.specialFeatures) === null || _values$specialFeatur === void 0 ? void 0 : _values$specialFeatur.split(',').map(f => f.trim()).filter(f => f)) || [],\n        accessRequirements: values.accessRequirements,\n        status: values.status\n      };\n      if (editingLocation) {\n        // 更新\n        setLocations(locations.map(l => l.id === editingLocation.id ? {\n          ...l,\n          ...processedValues\n        } : l));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newLocation = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setLocations([...locations, newLocation]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      continent: 'purple',\n      country: 'blue',\n      city: 'green',\n      town: 'cyan',\n      village: 'orange',\n      sect: 'red',\n      mountain: 'volcano',\n      forest: 'lime',\n      desert: 'gold',\n      ocean: 'geekblue'\n    };\n    return colors[type] || 'default';\n  };\n  const getSizeColor = size => {\n    const colors = {\n      tiny: 'default',\n      small: 'blue',\n      medium: 'green',\n      large: 'orange',\n      huge: 'red',\n      massive: 'purple'\n    };\n    return colors[size] || 'default';\n  };\n  const columns = [{\n    title: '位置名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'continent' ? '大陆' : record.type === 'country' ? '国家' : record.type === 'city' ? '城市' : record.type === 'town' ? '城镇' : record.type === 'village' ? '村庄' : record.type === 'sect' ? '宗门' : record.type === 'mountain' ? '山脉' : record.type === 'forest' ? '森林' : record.type === 'desert' ? '沙漠' : '海洋'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '规模',\n    dataIndex: 'size',\n    key: 'size',\n    render: size => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getSizeColor(size),\n      children: size === 'tiny' ? '微小' : size === 'small' ? '小型' : size === 'medium' ? '中型' : size === 'large' ? '大型' : size === 'huge' ? '巨大' : '超大'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '坐标',\n    key: 'coordinates',\n    render: (_, record) => {\n      var _record$coordinates, _record$coordinates2;\n      return /*#__PURE__*/_jsxDEV(Text, {\n        children: [\"(\", ((_record$coordinates = record.coordinates) === null || _record$coordinates === void 0 ? void 0 : _record$coordinates.x) || 0, \", \", ((_record$coordinates2 = record.coordinates) === null || _record$coordinates2 === void 0 ? void 0 : _record$coordinates2.y) || 0, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: '人口',\n    dataIndex: 'population',\n    key: 'population',\n    render: population => {\n      if (population >= 1000000) {\n        return `${(population / 1000000).toFixed(1)}M`;\n      } else if (population >= 1000) {\n        return `${(population / 1000).toFixed(1)}K`;\n      }\n      return (population === null || population === void 0 ? void 0 : population.toString()) || '0';\n    },\n    sorter: (a, b) => a.population - b.population\n  }, {\n    title: '危险等级',\n    dataIndex: 'dangerLevel',\n    key: 'dangerLevel',\n    render: level => {\n      const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: [level, \"/5\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 16\n      }, this);\n    },\n    sorter: (a, b) => a.dangerLevel - b.dangerLevel\n  }, {\n    title: '控制势力',\n    dataIndex: 'controllingFaction',\n    key: 'controllingFaction',\n    render: faction => faction || /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u65E0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 39\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'green' : status === 'sealed' ? 'orange' : 'red',\n      children: status === 'active' ? '开放' : status === 'sealed' ? '封印' : '毁坏'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u4F4D\\u7F6E\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 构建树形结构数据\n  const buildTreeData = locations => {\n    const locationMap = {};\n    const roots = [];\n\n    // 创建位置映射\n    locations.forEach(location => {\n      locationMap[location.id] = {\n        key: location.id,\n        title: location.name,\n        children: [],\n        ...location\n      };\n    });\n\n    // 构建树形结构\n    locations.forEach(location => {\n      if (location.parentId && locationMap[location.parentId]) {\n        locationMap[location.parentId].children.push(locationMap[location.id]);\n      } else {\n        roots.push(locationMap[location.id]);\n      }\n    });\n    return roots;\n  };\n  const treeData = buildTreeData(locations);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), \" \\u5730\\u56FE\\u7ED3\\u6784\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u4F4D\\u7F6E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4F4D\\u7F6E\\u603B\\u6570\",\n            value: locations.length,\n            prefix: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5927\\u9646\\u6570\\u91CF\",\n            value: locations.filter(l => l.type === 'continent').length,\n            prefix: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u57CE\\u5E02\\u6570\\u91CF\",\n            value: locations.filter(l => l.type === 'city').length,\n            prefix: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B97\\u95E8\\u6570\\u91CF\",\n            value: locations.filter(l => l.type === 'sect').length,\n            prefix: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5730\\u56FE\\u5C42\\u7EA7\\u7ED3\\u6784\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Tree, {\n            treeData: treeData,\n            defaultExpandAll: true,\n            showLine: true,\n            showIcon: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 16,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u4F4D\\u7F6E\\u5217\\u8868\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: columns,\n            dataSource: locations,\n            rowKey: \"id\",\n            loading: loading,\n            pagination: {\n              pageSize: 8,\n              showSizeChanger: true,\n              showQuickJumper: true,\n              showTotal: total => `共 ${total} 个位置`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingLocation ? '编辑位置' : '添加位置',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'city',\n          size: 'medium',\n          climate: 'temperate',\n          terrain: 'plain',\n          dangerLevel: 1,\n          status: 'active'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u4F4D\\u7F6E\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入位置名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F4D\\u7F6E\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u4F4D\\u7F6E\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择位置类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"continent\",\n                  children: \"\\u5927\\u9646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"country\",\n                  children: \"\\u56FD\\u5BB6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"city\",\n                  children: \"\\u57CE\\u5E02\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"town\",\n                  children: \"\\u57CE\\u9547\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"village\",\n                  children: \"\\u6751\\u5E84\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"sect\",\n                  children: \"\\u5B97\\u95E8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"mountain\",\n                  children: \"\\u5C71\\u8109\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"forest\",\n                  children: \"\\u68EE\\u6797\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"desert\",\n                  children: \"\\u6C99\\u6F20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"ocean\",\n                  children: \"\\u6D77\\u6D0B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"parentId\",\n              label: \"\\u4E0A\\u7EA7\\u4F4D\\u7F6E\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u4E0A\\u7EA7\\u4F4D\\u7F6E\",\n                allowClear: true,\n                children: locations.map(loc => /*#__PURE__*/_jsxDEV(Option, {\n                  value: loc.id,\n                  children: loc.name\n                }, loc.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"size\",\n              label: \"\\u89C4\\u6A21\",\n              rules: [{\n                required: true,\n                message: '请选择规模'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"tiny\",\n                  children: \"\\u5FAE\\u5C0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"small\",\n                  children: \"\\u5C0F\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"large\",\n                  children: \"\\u5927\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"huge\",\n                  children: \"\\u5DE8\\u5927\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"massive\",\n                  children: \"\\u8D85\\u5927\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"x\",\n              label: \"X\\u5750\\u6807\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"y\",\n              label: \"Y\\u5750\\u6807\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dangerLevel\",\n              label: \"\\u5371\\u9669\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择危险等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"climate\",\n              label: \"\\u6C14\\u5019\",\n              rules: [{\n                required: true,\n                message: '请选择气候'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"tropical\",\n                  children: \"\\u70ED\\u5E26\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"temperate\",\n                  children: \"\\u6E29\\u5E26\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"cold\",\n                  children: \"\\u5BD2\\u5E26\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"desert\",\n                  children: \"\\u6C99\\u6F20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"arctic\",\n                  children: \"\\u6781\\u5730\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"terrain\",\n              label: \"\\u5730\\u5F62\",\n              rules: [{\n                required: true,\n                message: '请选择地形'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"plain\",\n                  children: \"\\u5E73\\u539F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"mountain\",\n                  children: \"\\u5C71\\u5730\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"forest\",\n                  children: \"\\u68EE\\u6797\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"desert\",\n                  children: \"\\u6C99\\u6F20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"swamp\",\n                  children: \"\\u6CBC\\u6CFD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"coast\",\n                  children: \"\\u6D77\\u5CB8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"mixed\",\n                  children: \"\\u6DF7\\u5408\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u5F00\\u653E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"sealed\",\n                  children: \"\\u5C01\\u5370\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"destroyed\",\n                  children: \"\\u6BC1\\u574F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"population\",\n              label: \"\\u4EBA\\u53E3\\u6570\\u91CF\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"controllingFaction\",\n              label: \"\\u63A7\\u5236\\u52BF\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u63A7\\u5236\\u52BF\\u529B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"resources\",\n          label: \"\\u8D44\\u6E90\",\n          extra: \"\\u591A\\u4E2A\\u8D44\\u6E90\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u7075\\u77F3, \\u836F\\u6750, \\u77FF\\u7269\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"specialFeatures\",\n          label: \"\\u7279\\u6B8A\\u7279\\u5F81\",\n          extra: \"\\u591A\\u4E2A\\u7279\\u5F81\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u7075\\u6C14\\u6D53\\u90C1, \\u5929\\u5730\\u6CD5\\u5219\\u5B8C\\u6574\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"accessRequirements\",\n          label: \"\\u8FDB\\u5165\\u6761\\u4EF6\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8FDB\\u5165\\u6761\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u4F4D\\u7F6E\\u7684\\u7279\\u70B9\\u3001\\u5386\\u53F2\\u80CC\\u666F\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 372,\n    columnNumber: 5\n  }, this);\n};\n_s(MapStructure, \"CmTg1RKKILp6Kdj+JKI2XaxINqY=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = MapStructure;\nexport default MapStructure;\nvar _c;\n$RefreshReg$(_c, \"MapStructure\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Statistic", "Tree", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EnvironmentOutlined", "GlobalOutlined", "HomeOutlined", "BankOutlined", "ShopOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "MapStructure", "_s", "id", "projectId", "locations", "setLocations", "loading", "setLoading", "modalVisible", "setModalVisible", "editingLocation", "setEditingLocation", "form", "useForm", "mockLocations", "name", "type", "parentId", "coordinates", "x", "y", "size", "climate", "terrain", "population", "dangerLevel", "resources", "controllingFaction", "description", "specialFeatures", "accessRequirements", "status", "loadLocations", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "location", "_location$resources", "_location$specialFeat", "_location$coordinates", "_location$coordinates2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "handleDelete", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "some", "loc", "filter", "l", "success", "handleSubmit", "values", "_values$resources", "_values$specialFeatur", "processedValues", "split", "map", "r", "trim", "f", "newLocation", "Date", "now", "getTypeColor", "colors", "continent", "country", "city", "town", "village", "sect", "mountain", "forest", "desert", "ocean", "getSizeColor", "tiny", "small", "medium", "large", "huge", "massive", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "_", "_record$coordinates", "_record$coordinates2", "toFixed", "toString", "sorter", "a", "b", "level", "faction", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "buildTreeData", "locationMap", "roots", "for<PERSON>ach", "push", "treeData", "className", "gutter", "style", "marginBottom", "span", "value", "length", "prefix", "valueStyle", "defaultExpandAll", "showLine", "showIcon", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "allowClear", "min", "max", "extra", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/MapStructure.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Statistic,\n  Tree\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EnvironmentOutlined,\n  GlobalOutlined,\n  HomeOutlined,\n  BankOutlined,\n  ShopOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst MapStructure = () => {\n  const { id: projectId } = useParams();\n  const [locations, setLocations] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingLocation, setEditingLocation] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockLocations = [\n    {\n      id: 1,\n      name: '天元大陆',\n      type: 'continent',\n      parentId: null,\n      coordinates: { x: 0, y: 0 },\n      size: 'massive',\n      climate: 'temperate',\n      terrain: 'mixed',\n      population: ********,\n      dangerLevel: 2,\n      resources: ['灵石', '药材', '矿物'],\n      controllingFaction: '修仙联盟',\n      description: '修仙世界的主大陆，包含多个国家和宗门',\n      specialFeatures: ['灵气浓郁', '天地法则完整'],\n      accessRequirements: '无',\n      status: 'active'\n    },\n    {\n      id: 2,\n      name: '青云山脉',\n      type: 'mountain',\n      parentId: 1,\n      coordinates: { x: 100, y: 200 },\n      size: 'large',\n      climate: 'cold',\n      terrain: 'mountain',\n      population: 50000,\n      dangerLevel: 3,\n      resources: ['灵石矿', '雪莲', '寒冰晶'],\n      controllingFaction: '青云宗',\n      description: '连绵不绝的山脉，青云宗的总部所在地',\n      specialFeatures: ['常年云雾缭绕', '灵气充沛'],\n      accessRequirements: '青云宗弟子或邀请函',\n      status: 'active'\n    },\n    {\n      id: 3,\n      name: '青云宗',\n      type: 'sect',\n      parentId: 2,\n      coordinates: { x: 120, y: 220 },\n      size: 'medium',\n      climate: 'cold',\n      terrain: 'mountain',\n      population: 5000,\n      dangerLevel: 1,\n      resources: ['功法秘籍', '丹药', '法器'],\n      controllingFaction: '青云宗',\n      description: '正道第一大宗门，传承千年',\n      specialFeatures: ['护山大阵', '藏经阁', '炼丹房'],\n      accessRequirements: '宗门弟子或长老许可',\n      status: 'active'\n    }\n  ];\n\n  useEffect(() => {\n    loadLocations();\n  }, [projectId]);\n\n  const loadLocations = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setLocations(mockLocations);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载地图结构失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingLocation(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (location) => {\n    setEditingLocation(location);\n    form.setFieldsValue({\n      ...location,\n      resources: location.resources?.join(', '),\n      specialFeatures: location.specialFeatures?.join(', '),\n      x: location.coordinates?.x,\n      y: location.coordinates?.y\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 检查是否有子位置\n      const hasChildren = locations.some(loc => loc.parentId === id);\n      if (hasChildren) {\n        message.error('请先删除子位置');\n        return;\n      }\n\n      setLocations(locations.filter(l => l.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        parentId: values.parentId || null,\n        coordinates: {\n          x: values.x || 0,\n          y: values.y || 0\n        },\n        size: values.size,\n        climate: values.climate,\n        terrain: values.terrain,\n        population: values.population,\n        dangerLevel: values.dangerLevel,\n        resources: values.resources?.split(',').map(r => r.trim()).filter(r => r) || [],\n        controllingFaction: values.controllingFaction,\n        description: values.description,\n        specialFeatures: values.specialFeatures?.split(',').map(f => f.trim()).filter(f => f) || [],\n        accessRequirements: values.accessRequirements,\n        status: values.status\n      };\n\n      if (editingLocation) {\n        // 更新\n        setLocations(locations.map(l =>\n          l.id === editingLocation.id ? { ...l, ...processedValues } : l\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newLocation = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setLocations([...locations, newLocation]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      continent: 'purple',\n      country: 'blue',\n      city: 'green',\n      town: 'cyan',\n      village: 'orange',\n      sect: 'red',\n      mountain: 'volcano',\n      forest: 'lime',\n      desert: 'gold',\n      ocean: 'geekblue'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getSizeColor = (size) => {\n    const colors = {\n      tiny: 'default',\n      small: 'blue',\n      medium: 'green',\n      large: 'orange',\n      huge: 'red',\n      massive: 'purple'\n    };\n    return colors[size] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '位置名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'continent' ? '大陆' :\n             record.type === 'country' ? '国家' :\n             record.type === 'city' ? '城市' :\n             record.type === 'town' ? '城镇' :\n             record.type === 'village' ? '村庄' :\n             record.type === 'sect' ? '宗门' :\n             record.type === 'mountain' ? '山脉' :\n             record.type === 'forest' ? '森林' :\n             record.type === 'desert' ? '沙漠' : '海洋'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '规模',\n      dataIndex: 'size',\n      key: 'size',\n      render: (size) => (\n        <Tag color={getSizeColor(size)}>\n          {size === 'tiny' ? '微小' :\n           size === 'small' ? '小型' :\n           size === 'medium' ? '中型' :\n           size === 'large' ? '大型' :\n           size === 'huge' ? '巨大' : '超大'}\n        </Tag>\n      )\n    },\n    {\n      title: '坐标',\n      key: 'coordinates',\n      render: (_, record) => (\n        <Text>({record.coordinates?.x || 0}, {record.coordinates?.y || 0})</Text>\n      )\n    },\n    {\n      title: '人口',\n      dataIndex: 'population',\n      key: 'population',\n      render: (population) => {\n        if (population >= 1000000) {\n          return `${(population / 1000000).toFixed(1)}M`;\n        } else if (population >= 1000) {\n          return `${(population / 1000).toFixed(1)}K`;\n        }\n        return population?.toString() || '0';\n      },\n      sorter: (a, b) => a.population - b.population\n    },\n    {\n      title: '危险等级',\n      dataIndex: 'dangerLevel',\n      key: 'dangerLevel',\n      render: (level) => {\n        const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n        return <Tag color={color}>{level}/5</Tag>;\n      },\n      sorter: (a, b) => a.dangerLevel - b.dangerLevel\n    },\n    {\n      title: '控制势力',\n      dataIndex: 'controllingFaction',\n      key: 'controllingFaction',\n      render: (faction) => faction || <Text type=\"secondary\">无</Text>\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={status === 'active' ? 'green' : status === 'sealed' ? 'orange' : 'red'}>\n          {status === 'active' ? '开放' : status === 'sealed' ? '封印' : '毁坏'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个位置吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 构建树形结构数据\n  const buildTreeData = (locations) => {\n    const locationMap = {};\n    const roots = [];\n\n    // 创建位置映射\n    locations.forEach(location => {\n      locationMap[location.id] = {\n        key: location.id,\n        title: location.name,\n        children: [],\n        ...location\n      };\n    });\n\n    // 构建树形结构\n    locations.forEach(location => {\n      if (location.parentId && locationMap[location.parentId]) {\n        locationMap[location.parentId].children.push(locationMap[location.id]);\n      } else {\n        roots.push(locationMap[location.id]);\n      }\n    });\n\n    return roots;\n  };\n\n  const treeData = buildTreeData(locations);\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <GlobalOutlined /> 地图结构管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加位置\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"位置总数\"\n              value={locations.length}\n              prefix={<EnvironmentOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"大陆数量\"\n              value={locations.filter(l => l.type === 'continent').length}\n              prefix={<GlobalOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"城市数量\"\n              value={locations.filter(l => l.type === 'city').length}\n              prefix={<BankOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"宗门数量\"\n              value={locations.filter(l => l.type === 'sect').length}\n              prefix={<HomeOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={16}>\n        <Col span={8}>\n          <Card title=\"地图层级结构\" size=\"small\">\n            <Tree\n              treeData={treeData}\n              defaultExpandAll\n              showLine\n              showIcon={false}\n            />\n          </Card>\n        </Col>\n        <Col span={16}>\n          <Card title=\"位置列表\">\n            <Table\n              columns={columns}\n              dataSource={locations}\n              rowKey=\"id\"\n              loading={loading}\n              pagination={{\n                pageSize: 8,\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: (total) => `共 ${total} 个位置`\n              }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Modal\n        title={editingLocation ? '编辑位置' : '添加位置'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'city',\n            size: 'medium',\n            climate: 'temperate',\n            terrain: 'plain',\n            dangerLevel: 1,\n            status: 'active'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"位置名称\"\n                rules={[{ required: true, message: '请输入位置名称' }]}\n              >\n                <Input placeholder=\"请输入位置名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"位置类型\"\n                rules={[{ required: true, message: '请选择位置类型' }]}\n              >\n                <Select>\n                  <Option value=\"continent\">大陆</Option>\n                  <Option value=\"country\">国家</Option>\n                  <Option value=\"city\">城市</Option>\n                  <Option value=\"town\">城镇</Option>\n                  <Option value=\"village\">村庄</Option>\n                  <Option value=\"sect\">宗门</Option>\n                  <Option value=\"mountain\">山脉</Option>\n                  <Option value=\"forest\">森林</Option>\n                  <Option value=\"desert\">沙漠</Option>\n                  <Option value=\"ocean\">海洋</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"parentId\"\n                label=\"上级位置\"\n              >\n                <Select placeholder=\"请选择上级位置\" allowClear>\n                  {locations.map(loc => (\n                    <Option key={loc.id} value={loc.id}>{loc.name}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"size\"\n                label=\"规模\"\n                rules={[{ required: true, message: '请选择规模' }]}\n              >\n                <Select>\n                  <Option value=\"tiny\">微小</Option>\n                  <Option value=\"small\">小型</Option>\n                  <Option value=\"medium\">中型</Option>\n                  <Option value=\"large\">大型</Option>\n                  <Option value=\"huge\">巨大</Option>\n                  <Option value=\"massive\">超大</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item name=\"x\" label=\"X坐标\">\n                <InputNumber style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item name=\"y\" label=\"Y坐标\">\n                <InputNumber style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"dangerLevel\"\n                label=\"危险等级\"\n                rules={[{ required: true, message: '请选择危险等级' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"climate\"\n                label=\"气候\"\n                rules={[{ required: true, message: '请选择气候' }]}\n              >\n                <Select>\n                  <Option value=\"tropical\">热带</Option>\n                  <Option value=\"temperate\">温带</Option>\n                  <Option value=\"cold\">寒带</Option>\n                  <Option value=\"desert\">沙漠</Option>\n                  <Option value=\"arctic\">极地</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"terrain\"\n                label=\"地形\"\n                rules={[{ required: true, message: '请选择地形' }]}\n              >\n                <Select>\n                  <Option value=\"plain\">平原</Option>\n                  <Option value=\"mountain\">山地</Option>\n                  <Option value=\"forest\">森林</Option>\n                  <Option value=\"desert\">沙漠</Option>\n                  <Option value=\"swamp\">沼泽</Option>\n                  <Option value=\"coast\">海岸</Option>\n                  <Option value=\"mixed\">混合</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select>\n                  <Option value=\"active\">开放</Option>\n                  <Option value=\"sealed\">封印</Option>\n                  <Option value=\"destroyed\">毁坏</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item name=\"population\" label=\"人口数量\">\n                <InputNumber min={0} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item name=\"controllingFaction\" label=\"控制势力\">\n                <Input placeholder=\"请输入控制势力\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"resources\" label=\"资源\" extra=\"多个资源请用逗号分隔\">\n            <Input placeholder=\"如：灵石, 药材, 矿物\" />\n          </Form.Item>\n\n          <Form.Item name=\"specialFeatures\" label=\"特殊特征\" extra=\"多个特征请用逗号分隔\">\n            <Input placeholder=\"如：灵气浓郁, 天地法则完整\" />\n          </Form.Item>\n\n          <Form.Item name=\"accessRequirements\" label=\"进入条件\">\n            <Input placeholder=\"请输入进入条件\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <TextArea rows={3} placeholder=\"请描述位置的特点、历史背景等\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default MapStructure;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,SAAS,EACTC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAS,CAAC,GAAGzB,KAAK;AAC1B,MAAM;EAAE0B;AAAO,CAAC,GAAGzB,MAAM;AAEzB,MAAM0B,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGpC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG,CACpB;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,IAAI;IACdC,WAAW,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC3BC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,WAAW;IACpBC,OAAO,EAAE,OAAO;IAChBC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7BC,kBAAkB,EAAE,MAAM;IAC1BC,WAAW,EAAE,oBAAoB;IACjCC,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC;IACnCC,kBAAkB,EAAE,GAAG;IACvBC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/BC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;IAC/BC,kBAAkB,EAAE,KAAK;IACzBC,WAAW,EAAE,mBAAmB;IAChCC,eAAe,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;IACnCC,kBAAkB,EAAE,WAAW;IAC/BC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,CAAC;IACXC,WAAW,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/BC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,OAAO,EAAE,UAAU;IACnBC,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,CAAC;IACdC,SAAS,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,kBAAkB,EAAE,KAAK;IACzBC,WAAW,EAAE,cAAc;IAC3BC,eAAe,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;IACvCC,kBAAkB,EAAE,WAAW;IAC/BC,MAAM,EAAE;EACV,CAAC,CACF;EAEDjE,SAAS,CAAC,MAAM;IACdkE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EAEf,MAAM6B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA0B,UAAU,CAAC,MAAM;QACf5B,YAAY,CAACS,aAAa,CAAC;QAC3BP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;MACzB3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,SAAS,GAAGA,CAAA,KAAM;IACtBxB,kBAAkB,CAAC,IAAI,CAAC;IACxBC,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB3B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,UAAU,GAAIC,QAAQ,IAAK;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAC/B/B,kBAAkB,CAAC2B,QAAQ,CAAC;IAC5B1B,IAAI,CAAC+B,cAAc,CAAC;MAClB,GAAGL,QAAQ;MACXZ,SAAS,GAAAa,mBAAA,GAAED,QAAQ,CAACZ,SAAS,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBK,IAAI,CAAC,IAAI,CAAC;MACzCf,eAAe,GAAAW,qBAAA,GAAEF,QAAQ,CAACT,eAAe,cAAAW,qBAAA,uBAAxBA,qBAAA,CAA0BI,IAAI,CAAC,IAAI,CAAC;MACrDzB,CAAC,GAAAsB,qBAAA,GAAEH,QAAQ,CAACpB,WAAW,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBtB,CAAC;MAC1BC,CAAC,GAAAsB,sBAAA,GAAEJ,QAAQ,CAACpB,WAAW,cAAAwB,sBAAA,uBAApBA,sBAAA,CAAsBtB;IAC3B,CAAC,CAAC;IACFX,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoC,YAAY,GAAG,MAAO3C,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAM4C,WAAW,GAAG1C,SAAS,CAAC2C,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/B,QAAQ,KAAKf,EAAE,CAAC;MAC9D,IAAI4C,WAAW,EAAE;QACfhE,OAAO,CAACoD,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;MAEA7B,YAAY,CAACD,SAAS,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,EAAE,KAAKA,EAAE,CAAC,CAAC;MAChDpB,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MAAA,IAAAC,iBAAA,EAAAC,qBAAA;MACF,MAAMC,eAAe,GAAG;QACtBzC,IAAI,EAAEsC,MAAM,CAACtC,IAAI;QACjBC,IAAI,EAAEqC,MAAM,CAACrC,IAAI;QACjBC,QAAQ,EAAEoC,MAAM,CAACpC,QAAQ,IAAI,IAAI;QACjCC,WAAW,EAAE;UACXC,CAAC,EAAEkC,MAAM,CAAClC,CAAC,IAAI,CAAC;UAChBC,CAAC,EAAEiC,MAAM,CAACjC,CAAC,IAAI;QACjB,CAAC;QACDC,IAAI,EAAEgC,MAAM,CAAChC,IAAI;QACjBC,OAAO,EAAE+B,MAAM,CAAC/B,OAAO;QACvBC,OAAO,EAAE8B,MAAM,CAAC9B,OAAO;QACvBC,UAAU,EAAE6B,MAAM,CAAC7B,UAAU;QAC7BC,WAAW,EAAE4B,MAAM,CAAC5B,WAAW;QAC/BC,SAAS,EAAE,EAAA4B,iBAAA,GAAAD,MAAM,CAAC3B,SAAS,cAAA4B,iBAAA,uBAAhBA,iBAAA,CAAkBG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACX,MAAM,CAACU,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QAC/EhC,kBAAkB,EAAE0B,MAAM,CAAC1B,kBAAkB;QAC7CC,WAAW,EAAEyB,MAAM,CAACzB,WAAW;QAC/BC,eAAe,EAAE,EAAA0B,qBAAA,GAAAF,MAAM,CAACxB,eAAe,cAAA0B,qBAAA,uBAAtBA,qBAAA,CAAwBE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACX,MAAM,CAACY,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QAC3F/B,kBAAkB,EAAEuB,MAAM,CAACvB,kBAAkB;QAC7CC,MAAM,EAAEsB,MAAM,CAACtB;MACjB,CAAC;MAED,IAAIrB,eAAe,EAAE;QACnB;QACAL,YAAY,CAACD,SAAS,CAACsD,GAAG,CAACR,CAAC,IAC1BA,CAAC,CAAChD,EAAE,KAAKQ,eAAe,CAACR,EAAE,GAAG;UAAE,GAAGgD,CAAC;UAAE,GAAGM;QAAgB,CAAC,GAAGN,CAC/D,CAAC,CAAC;QACFpE,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMW,WAAW,GAAG;UAClB5D,EAAE,EAAE6D,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGR;QACL,CAAC;QACDnD,YAAY,CAAC,CAAC,GAAGD,SAAS,EAAE0D,WAAW,CAAC,CAAC;QACzChF,OAAO,CAACqE,OAAO,CAAC,MAAM,CAAC;MACzB;MACA1C,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAM+B,YAAY,GAAIjD,IAAI,IAAK;IAC7B,MAAMkD,MAAM,GAAG;MACbC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE,MAAM;MACdC,KAAK,EAAE;IACT,CAAC;IACD,OAAOV,MAAM,CAAClD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAM6D,YAAY,GAAIxD,IAAI,IAAK;IAC7B,MAAM6C,MAAM,GAAG;MACbY,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE;IACX,CAAC;IACD,OAAOjB,MAAM,CAAC7C,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAM+D,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB/F,OAAA,CAACnB,KAAK;MAAAmH,QAAA,gBACJhG,OAAA,CAACE,IAAI;QAAC+F,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BrG,OAAA,CAACf,GAAG;QAACqH,KAAK,EAAEhC,YAAY,CAACyB,MAAM,CAAC1E,IAAI,CAAE;QAAA2E,QAAA,EACnCD,MAAM,CAAC1E,IAAI,KAAK,WAAW,GAAG,IAAI,GAClC0E,MAAM,CAAC1E,IAAI,KAAK,SAAS,GAAG,IAAI,GAChC0E,MAAM,CAAC1E,IAAI,KAAK,MAAM,GAAG,IAAI,GAC7B0E,MAAM,CAAC1E,IAAI,KAAK,MAAM,GAAG,IAAI,GAC7B0E,MAAM,CAAC1E,IAAI,KAAK,SAAS,GAAG,IAAI,GAChC0E,MAAM,CAAC1E,IAAI,KAAK,MAAM,GAAG,IAAI,GAC7B0E,MAAM,CAAC1E,IAAI,KAAK,UAAU,GAAG,IAAI,GACjC0E,MAAM,CAAC1E,IAAI,KAAK,QAAQ,GAAG,IAAI,GAC/B0E,MAAM,CAAC1E,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;MAAI;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGnE,IAAI,iBACX1B,OAAA,CAACf,GAAG;MAACqH,KAAK,EAAEpB,YAAY,CAACxD,IAAI,CAAE;MAAAsE,QAAA,EAC5BtE,IAAI,KAAK,MAAM,GAAG,IAAI,GACtBA,IAAI,KAAK,OAAO,GAAG,IAAI,GACvBA,IAAI,KAAK,QAAQ,GAAG,IAAI,GACxBA,IAAI,KAAK,OAAO,GAAG,IAAI,GACvBA,IAAI,KAAK,MAAM,GAAG,IAAI,GAAG;IAAI;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAEA,CAACU,CAAC,EAAER,MAAM;MAAA,IAAAS,mBAAA,EAAAC,oBAAA;MAAA,oBAChBzG,OAAA,CAACE,IAAI;QAAA8F,QAAA,GAAC,GAAC,EAAC,EAAAQ,mBAAA,GAAAT,MAAM,CAACxE,WAAW,cAAAiF,mBAAA,uBAAlBA,mBAAA,CAAoBhF,CAAC,KAAI,CAAC,EAAC,IAAE,EAAC,EAAAiF,oBAAA,GAAAV,MAAM,CAACxE,WAAW,cAAAkF,oBAAA,uBAAlBA,oBAAA,CAAoBhF,CAAC,KAAI,CAAC,EAAC,GAAC;MAAA;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;EAE7E,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGhE,UAAU,IAAK;MACtB,IAAIA,UAAU,IAAI,OAAO,EAAE;QACzB,OAAO,GAAG,CAACA,UAAU,GAAG,OAAO,EAAE6E,OAAO,CAAC,CAAC,CAAC,GAAG;MAChD,CAAC,MAAM,IAAI7E,UAAU,IAAI,IAAI,EAAE;QAC7B,OAAO,GAAG,CAACA,UAAU,GAAG,IAAI,EAAE6E,OAAO,CAAC,CAAC,CAAC,GAAG;MAC7C;MACA,OAAO,CAAA7E,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE8E,QAAQ,CAAC,CAAC,KAAI,GAAG;IACtC,CAAC;IACDC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChF,UAAU,GAAGiF,CAAC,CAACjF;EACrC,CAAC,EACD;IACE6D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGkB,KAAK,IAAK;MACjB,MAAMT,KAAK,GAAGS,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;MAClG,oBAAO/G,OAAA,CAACf,GAAG;QAACqH,KAAK,EAAEA,KAAM;QAAAN,QAAA,GAAEe,KAAK,EAAC,IAAE;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3C,CAAC;IACDO,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/E,WAAW,GAAGgF,CAAC,CAAChF;EACtC,CAAC,EACD;IACE4D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,oBAAoB;IAC/BC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAGmB,OAAO,IAAKA,OAAO,iBAAIhH,OAAA,CAACE,IAAI;MAACmB,IAAI,EAAC,WAAW;MAAA2E,QAAA,EAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAChE,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGzD,MAAM,iBACbpC,OAAA,CAACf,GAAG;MAACqH,KAAK,EAAElE,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAGA,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAG,KAAM;MAAA4D,QAAA,EAChF5D,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAGA,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAI;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACU,CAAC,EAAER,MAAM,kBAChB/F,OAAA,CAACnB,KAAK;MAAAmH,QAAA,gBACJhG,OAAA,CAACZ,OAAO;QAACsG,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjBhG,OAAA,CAACzB,MAAM;UACL8C,IAAI,EAAC,MAAM;UACX4F,IAAI,eAAEjH,OAAA,CAACR,YAAY;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAMxE,UAAU,CAACqD,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVrG,OAAA,CAACd,UAAU;QACTwG,KAAK,EAAC,8DAAY;QAClByB,SAAS,EAAEA,CAAA,KAAMjE,YAAY,CAAC6C,MAAM,CAACxF,EAAE,CAAE;QACzC6G,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAArB,QAAA,eAEfhG,OAAA,CAACZ,OAAO;UAACsG,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjBhG,OAAA,CAACzB,MAAM;YACL8C,IAAI,EAAC,MAAM;YACXiG,MAAM;YACNL,IAAI,eAAEjH,OAAA,CAACP,cAAc;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMkB,aAAa,GAAI9G,SAAS,IAAK;IACnC,MAAM+G,WAAW,GAAG,CAAC,CAAC;IACtB,MAAMC,KAAK,GAAG,EAAE;;IAEhB;IACAhH,SAAS,CAACiH,OAAO,CAAC/E,QAAQ,IAAI;MAC5B6E,WAAW,CAAC7E,QAAQ,CAACpC,EAAE,CAAC,GAAG;QACzBqF,GAAG,EAAEjD,QAAQ,CAACpC,EAAE;QAChBmF,KAAK,EAAE/C,QAAQ,CAACvB,IAAI;QACpB4E,QAAQ,EAAE,EAAE;QACZ,GAAGrD;MACL,CAAC;IACH,CAAC,CAAC;;IAEF;IACAlC,SAAS,CAACiH,OAAO,CAAC/E,QAAQ,IAAI;MAC5B,IAAIA,QAAQ,CAACrB,QAAQ,IAAIkG,WAAW,CAAC7E,QAAQ,CAACrB,QAAQ,CAAC,EAAE;QACvDkG,WAAW,CAAC7E,QAAQ,CAACrB,QAAQ,CAAC,CAAC0E,QAAQ,CAAC2B,IAAI,CAACH,WAAW,CAAC7E,QAAQ,CAACpC,EAAE,CAAC,CAAC;MACxE,CAAC,MAAM;QACLkH,KAAK,CAACE,IAAI,CAACH,WAAW,CAAC7E,QAAQ,CAACpC,EAAE,CAAC,CAAC;MACtC;IACF,CAAC,CAAC;IAEF,OAAOkH,KAAK;EACd,CAAC;EAED,MAAMG,QAAQ,GAAGL,aAAa,CAAC9G,SAAS,CAAC;EAEzC,oBACET,OAAA;IAAK6H,SAAS,EAAC,SAAS;IAAA7B,QAAA,gBACtBhG,OAAA;MAAK6H,SAAS,EAAC,aAAa;MAAA7B,QAAA,gBAC1BhG,OAAA,CAACC,KAAK;QAAC8G,KAAK,EAAE,CAAE;QAACc,SAAS,EAAC,YAAY;QAAA7B,QAAA,gBACrChG,OAAA,CAACL,cAAc;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCACpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRrG,OAAA,CAACzB,MAAM;QACL8C,IAAI,EAAC,SAAS;QACd4F,IAAI,eAAEjH,OAAA,CAACT,YAAY;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAE1E,SAAU;QAAAwD,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENrG,OAAA,CAACjB,GAAG;MAAC+I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAhC,QAAA,gBACjDhG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACXhG,OAAA,CAAC3B,IAAI;UAACqD,IAAI,EAAC,OAAO;UAAAsE,QAAA,eAChBhG,OAAA,CAACX,SAAS;YACRqG,KAAK,EAAC,0BAAM;YACZwC,KAAK,EAAEzH,SAAS,CAAC0H,MAAO;YACxBC,MAAM,eAAEpI,OAAA,CAACN,mBAAmB;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACXhG,OAAA,CAAC3B,IAAI;UAACqD,IAAI,EAAC,OAAO;UAAAsE,QAAA,eAChBhG,OAAA,CAACX,SAAS;YACRqG,KAAK,EAAC,0BAAM;YACZwC,KAAK,EAAEzH,SAAS,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK,WAAW,CAAC,CAAC8G,MAAO;YAC5DC,MAAM,eAAEpI,OAAA,CAACL,cAAc;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BgC,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACXhG,OAAA,CAAC3B,IAAI;UAACqD,IAAI,EAAC,OAAO;UAAAsE,QAAA,eAChBhG,OAAA,CAACX,SAAS;YACRqG,KAAK,EAAC,0BAAM;YACZwC,KAAK,EAAEzH,SAAS,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK,MAAM,CAAC,CAAC8G,MAAO;YACvDC,MAAM,eAAEpI,OAAA,CAACH,YAAY;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBgC,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACXhG,OAAA,CAAC3B,IAAI;UAACqD,IAAI,EAAC,OAAO;UAAAsE,QAAA,eAChBhG,OAAA,CAACX,SAAS;YACRqG,KAAK,EAAC,0BAAM;YACZwC,KAAK,EAAEzH,SAAS,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK,MAAM,CAAC,CAAC8G,MAAO;YACvDC,MAAM,eAAEpI,OAAA,CAACJ,YAAY;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBgC,UAAU,EAAE;cAAE/B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrG,OAAA,CAACjB,GAAG;MAAC+I,MAAM,EAAE,EAAG;MAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,CAAE;QAAAjC,QAAA,eACXhG,OAAA,CAAC3B,IAAI;UAACqH,KAAK,EAAC,sCAAQ;UAAChE,IAAI,EAAC,OAAO;UAAAsE,QAAA,eAC/BhG,OAAA,CAACV,IAAI;YACHsI,QAAQ,EAAEA,QAAS;YACnBU,gBAAgB;YAChBC,QAAQ;YACRC,QAAQ,EAAE;UAAM;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrG,OAAA,CAAChB,GAAG;QAACiJ,IAAI,EAAE,EAAG;QAAAjC,QAAA,eACZhG,OAAA,CAAC3B,IAAI;UAACqH,KAAK,EAAC,0BAAM;UAAAM,QAAA,eAChBhG,OAAA,CAAC1B,KAAK;YACJmH,OAAO,EAAEA,OAAQ;YACjBgD,UAAU,EAAEhI,SAAU;YACtBiI,MAAM,EAAC,IAAI;YACX/H,OAAO,EAAEA,OAAQ;YACjBgI,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,eAAe,EAAE,IAAI;cACrBC,eAAe,EAAE,IAAI;cACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;YAClC;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrG,OAAA,CAACxB,KAAK;MACJkH,KAAK,EAAE3E,eAAe,GAAG,MAAM,GAAG,MAAO;MACzCkI,IAAI,EAAEpI,YAAa;MACnBqI,QAAQ,EAAEA,CAAA,KAAMpI,eAAe,CAAC,KAAK,CAAE;MACvCqI,IAAI,EAAEA,CAAA,KAAMlI,IAAI,CAACmI,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAE1I,OAAQ;MACxB2I,KAAK,EAAE,GAAI;MAAAtD,QAAA,eAEXhG,OAAA,CAACvB,IAAI;QACHwC,IAAI,EAAEA,IAAK;QACXsI,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE/F,YAAa;QACvBgG,aAAa,EAAE;UACbpI,IAAI,EAAE,MAAM;UACZK,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,WAAW;UACpBC,OAAO,EAAE,OAAO;UAChBE,WAAW,EAAE,CAAC;UACdM,MAAM,EAAE;QACV,CAAE;QAAA4D,QAAA,gBAEFhG,OAAA,CAACjB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6G,QAAA,eAEhDhG,OAAA,CAACtB,KAAK;gBAACoL,WAAW,EAAC;cAAS;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6G,QAAA,eAEhDhG,OAAA,CAACrB,MAAM;gBAAAqH,QAAA,gBACLhG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,WAAW;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,SAAS;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,SAAS;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,UAAU;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACjB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,UAAU;cACfuI,KAAK,EAAC,0BAAM;cAAA3D,QAAA,eAEZhG,OAAA,CAACrB,MAAM;gBAACmL,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAA/D,QAAA,EACrCvF,SAAS,CAACsD,GAAG,CAACV,GAAG,iBAChBrD,OAAA,CAACI,MAAM;kBAAc8H,KAAK,EAAE7E,GAAG,CAAC9C,EAAG;kBAAAyF,QAAA,EAAE3C,GAAG,CAACjC;gBAAI,GAAhCiC,GAAG,CAAC9C,EAAE;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,MAAM;cACXuI,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA6G,QAAA,eAE9ChG,OAAA,CAACrB,MAAM;gBAAAqH,QAAA,gBACLhG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,SAAS;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACjB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cAACtI,IAAI,EAAC,GAAG;cAACuI,KAAK,EAAC,eAAK;cAAA3D,QAAA,eAC7BhG,OAAA,CAACpB,WAAW;gBAACmJ,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cAACtI,IAAI,EAAC,GAAG;cAACuI,KAAK,EAAC,eAAK;cAAA3D,QAAA,eAC7BhG,OAAA,CAACpB,WAAW;gBAACmJ,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,aAAa;cAClBuI,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA6G,QAAA,eAEhDhG,OAAA,CAACpB,WAAW;gBAACoL,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClC,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACjB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,SAAS;cACduI,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA6G,QAAA,eAE9ChG,OAAA,CAACrB,MAAM;gBAAAqH,QAAA,gBACLhG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,UAAU;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,WAAW;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,MAAM;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,SAAS;cACduI,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA6G,QAAA,eAE9ChG,OAAA,CAACrB,MAAM;gBAAAqH,QAAA,gBACLhG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,UAAU;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,OAAO;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,CAAE;YAAAjC,QAAA,eACXhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cACRtI,IAAI,EAAC,QAAQ;cACbuI,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1K,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA6G,QAAA,eAE9ChG,OAAA,CAACrB,MAAM;gBAAAqH,QAAA,gBACLhG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,QAAQ;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCrG,OAAA,CAACI,MAAM;kBAAC8H,KAAK,EAAC,WAAW;kBAAAlC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACjB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACdhG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cAACtI,IAAI,EAAC,YAAY;cAACuI,KAAK,EAAC,0BAAM;cAAA3D,QAAA,eACvChG,OAAA,CAACpB,WAAW;gBAACoL,GAAG,EAAE,CAAE;gBAACjC,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNrG,OAAA,CAAChB,GAAG;YAACiJ,IAAI,EAAE,EAAG;YAAAjC,QAAA,eACZhG,OAAA,CAACvB,IAAI,CAACiL,IAAI;cAACtI,IAAI,EAAC,oBAAoB;cAACuI,KAAK,EAAC,0BAAM;cAAA3D,QAAA,eAC/ChG,OAAA,CAACtB,KAAK;gBAACoL,WAAW,EAAC;cAAS;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrG,OAAA,CAACvB,IAAI,CAACiL,IAAI;UAACtI,IAAI,EAAC,WAAW;UAACuI,KAAK,EAAC,cAAI;UAACO,KAAK,EAAC,8DAAY;UAAAlE,QAAA,eACvDhG,OAAA,CAACtB,KAAK;YAACoL,WAAW,EAAC;UAAc;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZrG,OAAA,CAACvB,IAAI,CAACiL,IAAI;UAACtI,IAAI,EAAC,iBAAiB;UAACuI,KAAK,EAAC,0BAAM;UAACO,KAAK,EAAC,8DAAY;UAAAlE,QAAA,eAC/DhG,OAAA,CAACtB,KAAK;YAACoL,WAAW,EAAC;UAAgB;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEZrG,OAAA,CAACvB,IAAI,CAACiL,IAAI;UAACtI,IAAI,EAAC,oBAAoB;UAACuI,KAAK,EAAC,0BAAM;UAAA3D,QAAA,eAC/ChG,OAAA,CAACtB,KAAK;YAACoL,WAAW,EAAC;UAAS;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZrG,OAAA,CAACvB,IAAI,CAACiL,IAAI;UACRtI,IAAI,EAAC,aAAa;UAClBuI,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1K,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6G,QAAA,eAE9ChG,OAAA,CAACG,QAAQ;YAACgK,IAAI,EAAE,CAAE;YAACL,WAAW,EAAC;UAAgB;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/F,EAAA,CAjmBID,YAAY;EAAA,QACUjC,SAAS,EAKpBK,IAAI,CAACyC,OAAO;AAAA;AAAAkJ,EAAA,GANvB/J,YAAY;AAmmBlB,eAAeA,YAAY;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}