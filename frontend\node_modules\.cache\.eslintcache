[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js": "19", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js": "20", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js": "21", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js": "22", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js": "23", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js": "24", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js": "25", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js": "26", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js": "27", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js": "28"}, {"size": 254, "mtime": 1748329246695, "results": "29", "hashOfConfig": "30"}, {"size": 4327, "mtime": 1748488644243, "results": "31", "hashOfConfig": "30"}, {"size": 6759, "mtime": 1748488468935, "results": "32", "hashOfConfig": "30"}, {"size": 9142, "mtime": 1748488507886, "results": "33", "hashOfConfig": "30"}, {"size": 18761, "mtime": 1748351266251, "results": "34", "hashOfConfig": "30"}, {"size": 10615, "mtime": 1748329052986, "results": "35", "hashOfConfig": "30"}, {"size": 9758, "mtime": 1748329098388, "results": "36", "hashOfConfig": "30"}, {"size": 20451, "mtime": 1748353392933, "results": "37", "hashOfConfig": "30"}, {"size": 29827, "mtime": 1748483630000, "results": "38", "hashOfConfig": "30"}, {"size": 24215, "mtime": 1748352877767, "results": "39", "hashOfConfig": "30"}, {"size": 23256, "mtime": 1748352524186, "results": "40", "hashOfConfig": "30"}, {"size": 22880, "mtime": 1748353123046, "results": "41", "hashOfConfig": "30"}, {"size": 44598, "mtime": 1748416357901, "results": "42", "hashOfConfig": "30"}, {"size": 19283, "mtime": 1748352994687, "results": "43", "hashOfConfig": "30"}, {"size": 4503, "mtime": 1748341004213, "results": "44", "hashOfConfig": "30"}, {"size": 26413, "mtime": 1748397029776, "results": "45", "hashOfConfig": "30"}, {"size": 5891, "mtime": 1748357289630, "results": "46", "hashOfConfig": "30"}, {"size": 8296, "mtime": 1748361420913, "results": "47", "hashOfConfig": "30"}, {"size": 16915, "mtime": 1748487765369, "results": "48", "hashOfConfig": "30"}, {"size": 18146, "mtime": 1748487204858, "results": "49", "hashOfConfig": "30"}, {"size": 15353, "mtime": 1748486959232, "results": "50", "hashOfConfig": "30"}, {"size": 15084, "mtime": 1748486881151, "results": "51", "hashOfConfig": "30"}, {"size": 14317, "mtime": 1748487034064, "results": "52", "hashOfConfig": "30"}, {"size": 17842, "mtime": 1748487380513, "results": "53", "hashOfConfig": "30"}, {"size": 21003, "mtime": 1748487480069, "results": "54", "hashOfConfig": "30"}, {"size": 19068, "mtime": 1748487295697, "results": "55", "hashOfConfig": "30"}, {"size": 9772, "mtime": 1748488625998, "results": "56", "hashOfConfig": "30"}, {"size": 19270, "mtime": 1748488679112, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["142", "143", "144", "145"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["146", "147", "148", "149"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", ["150"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["151"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["152"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["153", "154"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["155"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["156", "157"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["158", "159", "160"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["161", "162"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["163"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["164", "165"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["166"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js", ["167", "168", "169"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js", ["170", "171"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js", ["172"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js", ["173", "174"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js", ["175", "176"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js", ["177"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js", ["178"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js", ["179", "180"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js", ["181"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js", ["182", "183", "184", "185", "186", "187", "188"], [], {"ruleId": "189", "severity": 1, "message": "190", "line": 1, "column": 27, "nodeType": "191", "messageId": "192", "endLine": 1, "endColumn": 36}, {"ruleId": "189", "severity": 1, "message": "193", "line": 18, "column": 17, "nodeType": "191", "messageId": "192", "endLine": 18, "endColumn": 25}, {"ruleId": "189", "severity": 1, "message": "194", "line": 25, "column": 26, "nodeType": "191", "messageId": "192", "endLine": 25, "endColumn": 43}, {"ruleId": "189", "severity": 1, "message": "195", "line": 55, "column": 28, "nodeType": "191", "messageId": "192", "endLine": 55, "endColumn": 47}, {"ruleId": "189", "severity": 1, "message": "196", "line": 21, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 21, "endColumn": 9}, {"ruleId": "189", "severity": 1, "message": "197", "line": 34, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 34, "endColumn": 17}, {"ruleId": "189", "severity": 1, "message": "198", "line": 37, "column": 22, "nodeType": "191", "messageId": "192", "endLine": 37, "endColumn": 31}, {"ruleId": "199", "severity": 1, "message": "200", "line": 107, "column": 6, "nodeType": "201", "endLine": 107, "endColumn": 8, "suggestions": "202"}, {"ruleId": "199", "severity": 1, "message": "203", "line": 90, "column": 6, "nodeType": "201", "endLine": 90, "endColumn": 8, "suggestions": "204"}, {"ruleId": "199", "severity": 1, "message": "205", "line": 55, "column": 6, "nodeType": "201", "endLine": 55, "endColumn": 10, "suggestions": "206"}, {"ruleId": "199", "severity": 1, "message": "207", "line": 112, "column": 6, "nodeType": "201", "endLine": 112, "endColumn": 8, "suggestions": "208"}, {"ruleId": "189", "severity": 1, "message": "209", "line": 19, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 19, "endColumn": 15}, {"ruleId": "199", "severity": 1, "message": "210", "line": 116, "column": 6, "nodeType": "201", "endLine": 116, "endColumn": 8, "suggestions": "211"}, {"ruleId": "199", "severity": 1, "message": "212", "line": 131, "column": 6, "nodeType": "201", "endLine": 131, "endColumn": 8, "suggestions": "213"}, {"ruleId": "199", "severity": 1, "message": "214", "line": 109, "column": 6, "nodeType": "201", "endLine": 109, "endColumn": 8, "suggestions": "215"}, {"ruleId": "189", "severity": 1, "message": "216", "line": 344, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 344, "endColumn": 23}, {"ruleId": "189", "severity": 1, "message": "217", "line": 22, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 10}, {"ruleId": "189", "severity": 1, "message": "218", "line": 32, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 32, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "219", "line": 148, "column": 6, "nodeType": "201", "endLine": 148, "endColumn": 8, "suggestions": "220"}, {"ruleId": "189", "severity": 1, "message": "217", "line": 20, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 20, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "221", "line": 115, "column": 6, "nodeType": "201", "endLine": 115, "endColumn": 8, "suggestions": "222"}, {"ruleId": "189", "severity": 1, "message": "223", "line": 23, "column": 16, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 20}, {"ruleId": "189", "severity": 1, "message": "224", "line": 108, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 108, "endColumn": 19}, {"ruleId": "199", "severity": 1, "message": "225", "line": 371, "column": 6, "nodeType": "201", "endLine": 371, "endColumn": 8, "suggestions": "226"}, {"ruleId": "199", "severity": 1, "message": "227", "line": 133, "column": 6, "nodeType": "201", "endLine": 133, "endColumn": 8, "suggestions": "228"}, {"ruleId": "189", "severity": 1, "message": "229", "line": 22, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 11}, {"ruleId": "189", "severity": 1, "message": "230", "line": 33, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 33, "endColumn": 15}, {"ruleId": "199", "severity": 1, "message": "231", "line": 102, "column": 6, "nodeType": "201", "endLine": 102, "endColumn": 17, "suggestions": "232"}, {"ruleId": "189", "severity": 1, "message": "233", "line": 31, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 31, "endColumn": 17}, {"ruleId": "199", "severity": 1, "message": "234", "line": 112, "column": 6, "nodeType": "201", "endLine": 112, "endColumn": 17, "suggestions": "235"}, {"ruleId": "199", "severity": 1, "message": "236", "line": 87, "column": 6, "nodeType": "201", "endLine": 87, "endColumn": 17, "suggestions": "237"}, {"ruleId": "189", "severity": 1, "message": "229", "line": 21, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 21, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "238", "line": 81, "column": 6, "nodeType": "201", "endLine": 81, "endColumn": 17, "suggestions": "239"}, {"ruleId": "189", "severity": 1, "message": "229", "line": 22, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "240", "line": 87, "column": 6, "nodeType": "201", "endLine": 87, "endColumn": 17, "suggestions": "241"}, {"ruleId": "199", "severity": 1, "message": "242", "line": 112, "column": 6, "nodeType": "201", "endLine": 112, "endColumn": 17, "suggestions": "243"}, {"ruleId": "199", "severity": 1, "message": "244", "line": 140, "column": 6, "nodeType": "201", "endLine": 140, "endColumn": 17, "suggestions": "245"}, {"ruleId": "189", "severity": 1, "message": "246", "line": 31, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 31, "endColumn": 15}, {"ruleId": "199", "severity": 1, "message": "247", "line": 106, "column": 6, "nodeType": "201", "endLine": 106, "endColumn": 17, "suggestions": "248"}, {"ruleId": "189", "severity": 1, "message": "217", "line": 16, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 16, "endColumn": 10}, {"ruleId": "189", "severity": 1, "message": "249", "line": 33, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 33, "endColumn": 22}, {"ruleId": "189", "severity": 1, "message": "250", "line": 36, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 36, "endColumn": 22}, {"ruleId": "189", "severity": 1, "message": "251", "line": 37, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 37, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "252", "line": 43, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 43, "endColumn": 14}, {"ruleId": "189", "severity": 1, "message": "253", "line": 54, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 54, "endColumn": 24}, {"ruleId": "189", "severity": 1, "message": "254", "line": 55, "column": 10, "nodeType": "191", "messageId": "192", "endLine": 55, "endColumn": 26}, {"ruleId": "199", "severity": 1, "message": "255", "line": 149, "column": 6, "nodeType": "201", "endLine": 149, "endColumn": 8, "suggestions": "256"}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setStats' is assigned a value but never used.", "'setRecentProjects' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'Upload' is defined but never used.", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", "ArrayExpression", ["257"], "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["258"], "React Hook useEffect has a missing dependency: 'loadProject'. Either include it or remove the dependency array.", ["259"], "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["260"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["261"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["262"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["263"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["264"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["265"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["266"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["267"], "'Progress' is defined but never used.", "'StarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEquipment'. Either include it or remove the dependency array.", ["268"], "'ShieldOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPets'. Either include it or remove the dependency array.", ["269"], "React Hook useEffect has a missing dependency: 'loadRaces'. Either include it or remove the dependency array.", ["270"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["271"], "React Hook useEffect has a missing dependency: 'loadRealms'. Either include it or remove the dependency array.", ["272"], "React Hook useEffect has a missing dependency: 'loadDimensions'. Either include it or remove the dependency array.", ["273"], "React Hook useEffect has a missing dependency: 'loadTreasures'. Either include it or remove the dependency array.", ["274"], "'ShopOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", ["275"], "'ClockCircleOutlined' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'Panel' is assigned a value but never used.", "'editingChapter' is assigned a value but never used.", "'selectedVolumeId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockChapters' and 'mockVolumes'. Either include them or remove the dependency array.", ["276"], {"desc": "277", "fix": "278"}, {"desc": "279", "fix": "280"}, {"desc": "281", "fix": "282"}, {"desc": "283", "fix": "284"}, {"desc": "285", "fix": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, {"desc": "311", "fix": "312"}, {"desc": "313", "fix": "314"}, {"desc": "315", "fix": "316"}, "Update the dependencies array to be: [mockCharacters]", {"range": "317", "text": "318"}, "Update the dependencies array to be: [loadProjects]", {"range": "319", "text": "320"}, "Update the dependencies array to be: [id, loadProject]", {"range": "321", "text": "322"}, "Update the dependencies array to be: [mockFactions]", {"range": "323", "text": "324"}, "Update the dependencies array to be: [mockSettings]", {"range": "325", "text": "326"}, "Update the dependencies array to be: [mockSystems]", {"range": "327", "text": "328"}, "Update the dependencies array to be: [mockPlots]", {"range": "329", "text": "330"}, "Update the dependencies array to be: [mockEvents]", {"range": "331", "text": "332"}, "Update the dependencies array to be: [mockRelations]", {"range": "333", "text": "334"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "335", "text": "336"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "337", "text": "338"}, "Update the dependencies array to be: [loadEquipment, projectId]", {"range": "339", "text": "340"}, "Update the dependencies array to be: [loadPets, projectId]", {"range": "341", "text": "342"}, "Update the dependencies array to be: [loadRaces, projectId]", {"range": "343", "text": "344"}, "Update the dependencies array to be: [loadResources, projectId]", {"range": "345", "text": "346"}, "Update the dependencies array to be: [loadRealms, projectId]", {"range": "347", "text": "348"}, "Update the dependencies array to be: [loadDimensions, projectId]", {"range": "349", "text": "350"}, "Update the dependencies array to be: [loadTreasures, projectId]", {"range": "351", "text": "352"}, "Update the dependencies array to be: [loadLocations, projectId]", {"range": "353", "text": "354"}, "Update the dependencies array to be: [mockChapters, mockVolumes]", {"range": "355", "text": "356"}, [2366, 2368], "[mockCharacters]", [1783, 1785], "[loadProjects]", [1073, 1077], "[id, loadProject]", [2433, 2435], "[mockFactions]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2508, 2510], "[mockPlots]", [3453, 3455], "[mockEvents]", [2672, 2674], "[mockRelations]", [9595, 9597], "[fetchAIInfo]", [3499, 3501], "[testAllAPIs]", [2098, 2109], "[loadEquipment, projectId]", [2267, 2278], "[loadPets, projectId]", [1775, 1786], "[loadRaces, projectId]", [1716, 1727], "[loadResources, projectId]", [1850, 1861], "[loadRealms, projectId]", [2509, 2520], "[loadDimensions, projectId]", [3146, 3157], "[loadTreasures, projectId]", [2363, 2374], "[loadLocations, projectId]", [3501, 3503], "[mockChapters, mockVolumes]"]