{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\WorldSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Tooltip, Row, Col, Statistic, Descriptions, Tabs, Collapse } from 'antd';\nimport { PlusOutlined, GlobalOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, EnvironmentOutlined, HistoryOutlined, BankOutlined, TeamOutlined, BookOutlined, DollarOutlined, ShopOutlined, UsergroupAddOutlined, ThunderboltOutlined, ToolOutlined, HeartOutlined, CompassOutlined, GatewayOutlined, StarOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Panel\n} = Collapse;\nconst WorldSettings = () => {\n  _s();\n  const [settings, setSettings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingSetting, setEditingSetting] = useState(null);\n  const [viewingSetting, setViewingSetting] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟世界设定数据\n  const mockSettings = [{\n    id: 1,\n    name: '修仙世界基础设定',\n    category: 'world_basic',\n    description: '修仙世界的基本世界观和规则',\n    content: {\n      worldName: '九州大陆',\n      geography: '九州大陆分为九个州，每州都有不同的地理环境和修炼资源',\n      history: '上古时期，仙魔大战，仙界封印，修仙者只能在凡间修炼',\n      politics: '各大宗门割据一方，皇朝统治凡人，修仙者超然物外',\n      economy: '以灵石为主要货币，凡人使用金银铜钱',\n      culture: '尊师重道，强者为尊，追求长生不老'\n    },\n    status: 'active',\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-16'\n  }, {\n    id: 2,\n    name: '修炼体系',\n    category: 'cultivation',\n    description: '详细的修炼境界和体系',\n    content: {\n      realms: ['练气期', '筑基期', '金丹期', '元婴期', '化神期', '合体期', '大乘期', '渡劫期'],\n      methods: '吸收天地灵气，炼化为真元，淬炼肉身和神魂',\n      resources: '灵石、丹药、法器、功法、灵草',\n      bottlenecks: '每个大境界都有天劫考验',\n      lifespan: '每提升一个大境界，寿命大幅增加'\n    },\n    status: 'active',\n    createdAt: '2024-01-17',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    name: '宗门势力',\n    category: 'factions',\n    description: '各大宗门和势力的详细设定',\n    content: {\n      majorSects: ['青云宗', '天剑门', '万花谷', '血煞门'],\n      relationships: '正邪对立，内部也有竞争',\n      territories: '各占一方，互不侵犯',\n      resources: '控制灵脉和修炼资源',\n      hierarchy: '宗主、长老、内门弟子、外门弟子'\n    },\n    status: 'draft',\n    createdAt: '2024-01-19',\n    updatedAt: '2024-01-19'\n  }];\n  useEffect(() => {\n    setSettings(mockSettings);\n  }, []);\n\n  // 设定类型配置\n  const categoryConfig = {\n    world_basic: {\n      color: 'blue',\n      text: '世界基础',\n      icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 55\n      }, this)\n    },\n    geography: {\n      color: 'green',\n      text: '地理环境',\n      icon: /*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 54\n      }, this)\n    },\n    history: {\n      color: 'orange',\n      text: '历史文化',\n      icon: /*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 53\n      }, this)\n    },\n    politics: {\n      color: 'red',\n      text: '政治体系',\n      icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 51\n      }, this)\n    },\n    currency: {\n      color: 'gold',\n      text: '货币体系',\n      icon: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 52\n      }, this)\n    },\n    commerce: {\n      color: 'lime',\n      text: '商业体系',\n      icon: /*#__PURE__*/_jsxDEV(ShopOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 52\n      }, this)\n    },\n    races: {\n      color: 'magenta',\n      text: '种族类别',\n      icon: /*#__PURE__*/_jsxDEV(UsergroupAddOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 52\n      }, this)\n    },\n    martial_arts: {\n      color: 'volcano',\n      text: '功法体系',\n      icon: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 59\n      }, this)\n    },\n    equipment: {\n      color: 'orange',\n      text: '装备体系',\n      icon: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 55\n      }, this)\n    },\n    pets: {\n      color: 'pink',\n      text: '宠物体系',\n      icon: /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 48\n      }, this)\n    },\n    maps: {\n      color: 'cyan',\n      text: '地图结构',\n      icon: /*#__PURE__*/_jsxDEV(CompassOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 48\n      }, this)\n    },\n    dimensions: {\n      color: 'purple',\n      text: '维度结构',\n      icon: /*#__PURE__*/_jsxDEV(GatewayOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 56\n      }, this)\n    },\n    spiritual_treasures: {\n      color: 'gold',\n      text: '灵宝体系',\n      icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 63\n      }, this)\n    },\n    cultivation: {\n      color: 'geekblue',\n      text: '修炼体系',\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 59\n      }, this)\n    },\n    factions: {\n      color: 'lime',\n      text: '势力组织',\n      icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 52\n      }, this)\n    },\n    economy: {\n      color: 'green',\n      text: '经济制度',\n      icon: /*#__PURE__*/_jsxDEV(BankOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 52\n      }, this)\n    },\n    other: {\n      color: 'default',\n      text: '其他',\n      icon: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 50\n      }, this)\n    }\n  };\n\n  // 表格列配置\n  const columns = [{\n    title: '设定名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [categoryConfig[record.category].icon, /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '类型',\n    dataIndex: 'category',\n    key: 'category',\n    render: category => /*#__PURE__*/_jsxDEV(Tag, {\n      color: categoryConfig[category].color,\n      children: categoryConfig[category].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 9\n    }, this),\n    filters: Object.keys(categoryConfig).map(key => ({\n      text: categoryConfig[key].text,\n      value: key\n    })),\n    onFilter: (value, record) => record.category === value\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    ellipsis: true\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 'active' ? 'green' : 'orange',\n      children: status === 'active' ? '已完成' : '草稿'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerate(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u8BBE\\u5B9A\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理新建/编辑设定\n  const handleCreateOrEdit = () => {\n    setEditingSetting(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = setting => {\n    setEditingSetting(setting);\n    form.setFieldsValue({\n      ...setting,\n      ...setting.content\n    });\n    setModalVisible(true);\n  };\n  const handleView = setting => {\n    setViewingSetting(setting);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = setting => {\n    message.info(`AI生成世界设定：${setting.name}`);\n  };\n  const handleDelete = id => {\n    setSettings(settings.filter(s => s.id !== id));\n    message.success('设定删除成功');\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 分离基本信息和内容\n      const {\n        name,\n        category,\n        description,\n        status,\n        ...content\n      } = values;\n      const processedValues = {\n        name,\n        category,\n        description,\n        status,\n        content\n      };\n      if (editingSetting) {\n        // 编辑设定\n        setSettings(settings.map(s => s.id === editingSetting.id ? {\n          ...s,\n          ...processedValues,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : s));\n        message.success('设定更新成功');\n      } else {\n        // 新建设定\n        const newSetting = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setSettings([...settings, newSetting]);\n        message.success('设定创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalSettings = settings.length;\n  const activeSettings = settings.filter(s => s.status === 'active').length;\n  const draftSettings = settings.filter(s => s.status === 'draft').length;\n  const categoryStats = Object.keys(categoryConfig).reduce((acc, key) => {\n    acc[key] = settings.filter(s => s.category === key).length;\n    return acc;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u4E16\\u754C\\u8BBE\\u5B9A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u8BBE\\u5B9A\\u6570\",\n            value: totalSettings,\n            prefix: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: activeSettings,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8349\\u7A3F\",\n            value: draftSettings,\n            prefix: /*#__PURE__*/_jsxDEV(EditOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4FEE\\u70BC\\u4F53\\u7CFB\",\n            value: categoryStats.cultivation || 0,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u8BBE\\u5B9A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: settings,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个设定`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingSetting ? '编辑世界设定' : '新建世界设定',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 1000,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          category: 'world_basic',\n          status: 'draft'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u8BBE\\u5B9A\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入设定名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5B9A\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u8BBE\\u5B9A\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择设定类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: Object.keys(categoryConfig).map(key => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: categoryConfig[key].text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5B9A\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入设定描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u7B80\\u8981\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u8BBE\\u5B9A\\u7684\\u5185\\u5BB9\\u548C\\u4F5C\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"draft\",\n              children: \"\\u8349\\u7A3F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"active\",\n              children: \"\\u5DF2\\u5B8C\\u6210\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          defaultActiveKey: \"basic\",\n          children: [/*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u57FA\\u7840\\u4FE1\\u606F\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"worldName\",\n              label: \"\\u4E16\\u754C\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u4E5D\\u5DDE\\u5927\\u9646\\u3001\\u4FEE\\u4ED9\\u754C\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"overview\",\n              label: \"\\u4E16\\u754C\\u6982\\u8FF0\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u6574\\u4F53\\u60C5\\u51B5\\u548C\\u7279\\u8272...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, \"basic\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5730\\u7406\\u73AF\\u5883\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"geography\",\n              label: \"\\u5730\\u7406\\u63CF\\u8FF0\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u5730\\u7406\\u73AF\\u5883\\u3001\\u5730\\u5F62\\u5730\\u8C8C...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"climate\",\n              label: \"\\u6C14\\u5019\\u73AF\\u5883\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u6C14\\u5019\\u7279\\u70B9\\u3001\\u5B63\\u8282\\u53D8\\u5316...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)]\n          }, \"geography\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5386\\u53F2\\u6587\\u5316\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"history\",\n              label: \"\\u5386\\u53F2\\u80CC\\u666F\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u5386\\u53F2\\u53D1\\u5C55\\u3001\\u91CD\\u5927\\u4E8B\\u4EF6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"culture\",\n              label: \"\\u6587\\u5316\\u7279\\u8272\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u6587\\u5316\\u4F20\\u7EDF\\u3001\\u4EF7\\u503C\\u89C2\\u5FF5...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, \"history\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u653F\\u6CBB\\u7ECF\\u6D4E\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"politics\",\n              label: \"\\u653F\\u6CBB\\u4F53\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u653F\\u6CBB\\u5236\\u5EA6\\u3001\\u6743\\u529B\\u7ED3\\u6784...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"economy\",\n              label: \"\\u7ECF\\u6D4E\\u5236\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7ECF\\u6D4E\\u4F53\\u7CFB\\u3001\\u8D27\\u5E01\\u5236\\u5EA6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this)]\n          }, \"politics\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u8D27\\u5E01\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"currencyName\",\n              label: \"\\u4E3B\\u8981\\u8D27\\u5E01\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u7075\\u77F3\\u3001\\u91D1\\u5E01\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"currencySystem\",\n              label: \"\\u8D27\\u5E01\\u5236\\u5EA6\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u8D27\\u5E01\\u5236\\u5EA6\\u3001\\u6C47\\u7387\\u4F53\\u7CFB...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"monetaryPolicy\",\n              label: \"\\u8D27\\u5E01\\u653F\\u7B56\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u53D1\\u884C\\u673A\\u6784\\u3001\\u76D1\\u7BA1\\u5236\\u5EA6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this)]\n          }, \"currency\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5546\\u4E1A\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"tradeRoutes\",\n              label: \"\\u8D38\\u6613\\u8DEF\\u7EBF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4E3B\\u8981\\u8D38\\u6613\\u8DEF\\u7EBF\\u3001\\u5546\\u4E1A\\u7F51\\u7EDC...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"guilds\",\n              label: \"\\u5546\\u4F1A\\u7EC4\\u7EC7\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5546\\u4F1A\\u3001\\u516C\\u4F1A\\u7B49\\u5546\\u4E1A\\u7EC4\\u7EC7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"marketRules\",\n              label: \"\\u5E02\\u573A\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5E02\\u573A\\u673A\\u5236\\u3001\\u5546\\u4E1A\\u6CD5\\u89C4...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this)]\n          }, \"commerce\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u79CD\\u65CF\\u7C7B\\u522B\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"majorRaces\",\n              label: \"\\u4E3B\\u8981\\u79CD\\u65CF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u4E2D\\u7684\\u4E3B\\u8981\\u79CD\\u65CF...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"raceRelations\",\n              label: \"\\u79CD\\u65CF\\u5173\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u79CD\\u65CF\\u95F4\\u7684\\u5173\\u7CFB\\u3001\\u51B2\\u7A81...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"raceTraits\",\n              label: \"\\u79CD\\u65CF\\u7279\\u5F81\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5404\\u79CD\\u65CF\\u7684\\u7279\\u6B8A\\u80FD\\u529B\\u3001\\u6587\\u5316...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, \"races\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u529F\\u6CD5\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"techniqueTypes\",\n              label: \"\\u529F\\u6CD5\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u529F\\u6CD5\\u7684\\u5206\\u7C7B\\u3001\\u7B49\\u7EA7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"cultivationMethods\",\n              label: \"\\u4FEE\\u70BC\\u65B9\\u6CD5\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4FEE\\u70BC\\u65B9\\u5F0F\\u3001\\u8D44\\u6E90\\u9700\\u6C42...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"martialArtsRules\",\n              label: \"\\u529F\\u6CD5\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u529F\\u6CD5\\u7684\\u9650\\u5236\\u3001\\u7981\\u5FCC...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this)]\n          }, \"martial_arts\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u88C5\\u5907\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"equipmentTypes\",\n              label: \"\\u88C5\\u5907\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u88C5\\u5907\\u7684\\u7C7B\\u578B\\u3001\\u54C1\\u7EA7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"enhancementSystem\",\n              label: \"\\u5F3A\\u5316\\u7CFB\\u7EDF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u88C5\\u5907\\u5F3A\\u5316\\u3001\\u5347\\u7EA7\\u673A\\u5236...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"craftingSystem\",\n              label: \"\\u5236\\u4F5C\\u7CFB\\u7EDF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u88C5\\u5907\\u5236\\u4F5C\\u3001\\u6750\\u6599\\u9700\\u6C42...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, \"equipment\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5BA0\\u7269\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"petTypes\",\n              label: \"\\u5BA0\\u7269\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5BA0\\u7269\\u7684\\u79CD\\u7C7B\\u3001\\u7A00\\u6709\\u5EA6...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"petEvolution\",\n              label: \"\\u8FDB\\u5316\\u7CFB\\u7EDF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5BA0\\u7269\\u8FDB\\u5316\\u3001\\u6210\\u957F\\u673A\\u5236...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"petSkills\",\n              label: \"\\u6280\\u80FD\\u7CFB\\u7EDF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5BA0\\u7269\\u6280\\u80FD\\u3001\\u57F9\\u517B\\u65B9\\u5F0F...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this)]\n          }, \"pets\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u5730\\u56FE\\u7ED3\\u6784\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"mapHierarchy\",\n              label: \"\\u5730\\u56FE\\u5C42\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5730\\u56FE\\u7684\\u5C42\\u7EA7\\u7ED3\\u6784...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"terrainTypes\",\n              label: \"\\u5730\\u5F62\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5404\\u79CD\\u5730\\u5F62\\u7279\\u5F81...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"specialAreas\",\n              label: \"\\u7279\\u6B8A\\u533A\\u57DF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7279\\u6B8A\\u5730\\u70B9\\u3001\\u79D8\\u5883...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this)]\n          }, \"maps\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7EF4\\u5EA6\\u7ED3\\u6784\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dimensionTypes\",\n              label: \"\\u7EF4\\u5EA6\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4E0D\\u540C\\u7EF4\\u5EA6\\u7684\\u7279\\u5F81...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dimensionLaws\",\n              label: \"\\u7EF4\\u5EA6\\u6CD5\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5404\\u7EF4\\u5EA6\\u7684\\u7269\\u7406\\u6CD5\\u5219...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dimensionTravel\",\n              label: \"\\u7EF4\\u5EA6\\u7A7F\\u8D8A\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7EF4\\u5EA6\\u95F4\\u7684\\u65C5\\u884C\\u65B9\\u5F0F...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this)]\n          }, \"dimensions\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7075\\u5B9D\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"treasureGrades\",\n              label: \"\\u7075\\u5B9D\\u54C1\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7075\\u5B9D\\u7684\\u54C1\\u7EA7\\u5212\\u5206...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 702,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"spiritSystem\",\n              label: \"\\u5668\\u7075\\u7CFB\\u7EDF\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u5668\\u7075\\u7684\\u89C9\\u9192\\u3001\\u80FD\\u529B...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"refiningMethods\",\n              label: \"\\u70BC\\u5236\\u65B9\\u6CD5\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u7075\\u5B9D\\u7684\\u70BC\\u5236\\u3001\\u5347\\u7EA7...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, \"spiritual_treasures\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n            tab: \"\\u7279\\u6B8A\\u89C4\\u5219\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"rules\",\n              label: \"\\u4E16\\u754C\\u89C4\\u5219\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 4,\n                placeholder: \"\\u63CF\\u8FF0\\u4E16\\u754C\\u7684\\u7279\\u6B8A\\u89C4\\u5219\\u3001\\u6CD5\\u5219...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"magic\",\n              label: \"\\u529B\\u91CF\\u4F53\\u7CFB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u63CF\\u8FF0\\u4FEE\\u70BC\\u3001\\u9B54\\u6CD5\\u7B49\\u529B\\u91CF\\u4F53\\u7CFB...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this)]\n          }, \"rules\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 724,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4E16\\u754C\\u8BBE\\u5B9A\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 11\n      }, this)],\n      width: 1000,\n      children: viewingSetting && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            span: 24,\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              children: viewingSetting.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: categoryConfig[viewingSetting.category].color,\n                children: categoryConfig[viewingSetting.category].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: viewingSetting.status === 'active' ? 'green' : 'orange',\n                children: viewingSetting.status === 'active' ? '已完成' : '草稿'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              style: {\n                marginTop: 16\n              },\n              children: viewingSetting.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n          defaultActiveKey: ['1'],\n          children: [viewingSetting.content.worldName && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u4E16\\u754C\\u540D\\u79F0\",\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              children: viewingSetting.content.worldName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 19\n            }, this)\n          }, \"1\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 17\n          }, this), viewingSetting.content.geography && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5730\\u7406\\u73AF\\u5883\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.geography\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 19\n            }, this)\n          }, \"2\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 17\n          }, this), viewingSetting.content.history && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5386\\u53F2\\u80CC\\u666F\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.history\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 19\n            }, this)\n          }, \"3\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 17\n          }, this), viewingSetting.content.politics && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u653F\\u6CBB\\u4F53\\u7CFB\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.politics\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 19\n            }, this)\n          }, \"4\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 17\n          }, this), viewingSetting.content.economy && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u7ECF\\u6D4E\\u5236\\u5EA6\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.economy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 19\n            }, this)\n          }, \"5\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 17\n          }, this), viewingSetting.content.culture && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u6587\\u5316\\u7279\\u8272\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.culture\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 19\n            }, this)\n          }, \"6\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 17\n          }, this), viewingSetting.content.currencySystem && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u8D27\\u5E01\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.currencySystem\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 19\n            }, this), viewingSetting.content.currencyName && /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: [\"\\u4E3B\\u8981\\u8D27\\u5E01\\uFF1A\", viewingSetting.content.currencyName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 21\n            }, this)]\n          }, \"7\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 17\n          }, this), viewingSetting.content.tradeRoutes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5546\\u4E1A\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.tradeRoutes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 19\n            }, this), viewingSetting.content.guilds && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5546\\u4F1A\\u7EC4\\u7EC7\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 32\n              }, this), viewingSetting.content.guilds]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 21\n            }, this)]\n          }, \"8\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 17\n          }, this), viewingSetting.content.majorRaces && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u79CD\\u65CF\\u7C7B\\u522B\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.majorRaces\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 19\n            }, this), viewingSetting.content.raceRelations && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u79CD\\u65CF\\u5173\\u7CFB\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 32\n              }, this), viewingSetting.content.raceRelations]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 21\n            }, this)]\n          }, \"9\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 830,\n            columnNumber: 17\n          }, this), viewingSetting.content.techniqueTypes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u529F\\u6CD5\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.techniqueTypes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 19\n            }, this), viewingSetting.content.cultivationMethods && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u4FEE\\u70BC\\u65B9\\u6CD5\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 32\n              }, this), viewingSetting.content.cultivationMethods]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 21\n            }, this)]\n          }, \"10\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 17\n          }, this), viewingSetting.content.equipmentTypes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u88C5\\u5907\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.equipmentTypes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 19\n            }, this), viewingSetting.content.enhancementSystem && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5F3A\\u5316\\u7CFB\\u7EDF\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 851,\n                columnNumber: 32\n              }, this), viewingSetting.content.enhancementSystem]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 21\n            }, this)]\n          }, \"11\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 17\n          }, this), viewingSetting.content.petTypes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5BA0\\u7269\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.petTypes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 19\n            }, this), viewingSetting.content.petEvolution && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u8FDB\\u5316\\u7CFB\\u7EDF\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 32\n              }, this), viewingSetting.content.petEvolution]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 21\n            }, this)]\n          }, \"12\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 17\n          }, this), viewingSetting.content.mapHierarchy && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u5730\\u56FE\\u7ED3\\u6784\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.mapHierarchy\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 19\n            }, this), viewingSetting.content.terrainTypes && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5730\\u5F62\\u7C7B\\u578B\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 32\n              }, this), viewingSetting.content.terrainTypes]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 21\n            }, this)]\n          }, \"13\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 17\n          }, this), viewingSetting.content.dimensionTypes && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u7EF4\\u5EA6\\u7ED3\\u6784\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.dimensionTypes\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 19\n            }, this), viewingSetting.content.dimensionLaws && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u7EF4\\u5EA6\\u6CD5\\u5219\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 32\n              }, this), viewingSetting.content.dimensionLaws]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 21\n            }, this)]\n          }, \"14\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 17\n          }, this), viewingSetting.content.treasureGrades && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u7075\\u5B9D\\u4F53\\u7CFB\",\n            children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.treasureGrades\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 19\n            }, this), viewingSetting.content.spiritSystem && /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5668\\u7075\\u7CFB\\u7EDF\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 32\n              }, this), viewingSetting.content.spiritSystem]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 21\n            }, this)]\n          }, \"15\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 17\n          }, this), viewingSetting.content.rules && /*#__PURE__*/_jsxDEV(Panel, {\n            header: \"\\u4E16\\u754C\\u89C4\\u5219\",\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingSetting.content.rules\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 19\n            }, this)\n          }, \"16\", false, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 774,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n};\n_s(WorldSettings, \"/Ahf01ykd3KMvsM1U7aStmquoFE=\", false, function () {\n  return [Form.useForm];\n});\n_c = WorldSettings;\nexport default WorldSettings;\nvar _c;\n$RefreshReg$(_c, \"WorldSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "Tabs", "Collapse", "PlusOutlined", "GlobalOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "EnvironmentOutlined", "HistoryOutlined", "BankOutlined", "TeamOutlined", "BookOutlined", "DollarOutlined", "ShopOutlined", "UsergroupAddOutlined", "ThunderboltOutlined", "ToolOutlined", "HeartOutlined", "CompassOutlined", "GatewayOutlined", "StarOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "TabPane", "Panel", "WorldSettings", "_s", "settings", "setSettings", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingSetting", "setEditingSetting", "viewingSetting", "setViewingSetting", "form", "useForm", "mockSettings", "id", "name", "category", "description", "content", "worldName", "geography", "history", "politics", "economy", "culture", "status", "createdAt", "updatedAt", "realms", "methods", "resources", "bottlenecks", "lifespan", "majorSects", "relationships", "territories", "hierarchy", "categoryConfig", "world_basic", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currency", "commerce", "races", "martial_arts", "equipment", "pets", "maps", "dimensions", "spiritual_treasures", "cultivation", "factions", "other", "columns", "title", "dataIndex", "key", "render", "record", "children", "strong", "filters", "Object", "keys", "map", "value", "onFilter", "ellipsis", "sorter", "a", "b", "Date", "_", "type", "onClick", "handleView", "handleEdit", "handleAIGenerate", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleCreateOrEdit", "resetFields", "setting", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "filter", "s", "success", "handleModalOk", "values", "validateFields", "processedValues", "toISOString", "split", "newSetting", "now", "error", "console", "totalSettings", "length", "activeSettings", "draftSettings", "categoryStats", "reduce", "acc", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "confirmLoading", "width", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "rows", "defaultActiveKey", "tab", "footer", "marginTop", "header", "currencySystem", "currencyName", "tradeRoutes", "guilds", "majorRaces", "raceRelations", "techniqueTypes", "cultivationMethods", "equipmentTypes", "enhancementSystem", "petTypes", "petEvolution", "mapHierarchy", "terrainTypes", "dimensionTypes", "dimensionLaws", "treasureGrades", "spiritSystem", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/WorldSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  Tabs,\n  Collapse\n} from 'antd';\nimport {\n  PlusOutlined,\n  GlobalOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  EnvironmentOutlined,\n  HistoryOutlined,\n  BankOutlined,\n  TeamOutlined,\n  BookOutlined,\n  DollarOutlined,\n  ShopOutlined,\n  UsergroupAddOutlined,\n  ThunderboltOutlined,\n  ToolOutlined,\n  HeartOutlined,\n  CompassOutlined,\n  GatewayOutlined,\n  StarOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { TabPane } = Tabs;\nconst { Panel } = Collapse;\n\nconst WorldSettings = () => {\n  const [settings, setSettings] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingSetting, setEditingSetting] = useState(null);\n  const [viewingSetting, setViewingSetting] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟世界设定数据\n  const mockSettings = [\n    {\n      id: 1,\n      name: '修仙世界基础设定',\n      category: 'world_basic',\n      description: '修仙世界的基本世界观和规则',\n      content: {\n        worldName: '九州大陆',\n        geography: '九州大陆分为九个州，每州都有不同的地理环境和修炼资源',\n        history: '上古时期，仙魔大战，仙界封印，修仙者只能在凡间修炼',\n        politics: '各大宗门割据一方，皇朝统治凡人，修仙者超然物外',\n        economy: '以灵石为主要货币，凡人使用金银铜钱',\n        culture: '尊师重道，强者为尊，追求长生不老'\n      },\n      status: 'active',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    },\n    {\n      id: 2,\n      name: '修炼体系',\n      category: 'cultivation',\n      description: '详细的修炼境界和体系',\n      content: {\n        realms: ['练气期', '筑基期', '金丹期', '元婴期', '化神期', '合体期', '大乘期', '渡劫期'],\n        methods: '吸收天地灵气，炼化为真元，淬炼肉身和神魂',\n        resources: '灵石、丹药、法器、功法、灵草',\n        bottlenecks: '每个大境界都有天劫考验',\n        lifespan: '每提升一个大境界，寿命大幅增加'\n      },\n      status: 'active',\n      createdAt: '2024-01-17',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      name: '宗门势力',\n      category: 'factions',\n      description: '各大宗门和势力的详细设定',\n      content: {\n        majorSects: ['青云宗', '天剑门', '万花谷', '血煞门'],\n        relationships: '正邪对立，内部也有竞争',\n        territories: '各占一方，互不侵犯',\n        resources: '控制灵脉和修炼资源',\n        hierarchy: '宗主、长老、内门弟子、外门弟子'\n      },\n      status: 'draft',\n      createdAt: '2024-01-19',\n      updatedAt: '2024-01-19'\n    }\n  ];\n\n  useEffect(() => {\n    setSettings(mockSettings);\n  }, []);\n\n  // 设定类型配置\n  const categoryConfig = {\n    world_basic: { color: 'blue', text: '世界基础', icon: <GlobalOutlined /> },\n    geography: { color: 'green', text: '地理环境', icon: <EnvironmentOutlined /> },\n    history: { color: 'orange', text: '历史文化', icon: <HistoryOutlined /> },\n    politics: { color: 'red', text: '政治体系', icon: <BankOutlined /> },\n    currency: { color: 'gold', text: '货币体系', icon: <DollarOutlined /> },\n    commerce: { color: 'lime', text: '商业体系', icon: <ShopOutlined /> },\n    races: { color: 'magenta', text: '种族类别', icon: <UsergroupAddOutlined /> },\n    martial_arts: { color: 'volcano', text: '功法体系', icon: <ThunderboltOutlined /> },\n    equipment: { color: 'orange', text: '装备体系', icon: <ToolOutlined /> },\n    pets: { color: 'pink', text: '宠物体系', icon: <HeartOutlined /> },\n    maps: { color: 'cyan', text: '地图结构', icon: <CompassOutlined /> },\n    dimensions: { color: 'purple', text: '维度结构', icon: <GatewayOutlined /> },\n    spiritual_treasures: { color: 'gold', text: '灵宝体系', icon: <StarOutlined /> },\n    cultivation: { color: 'geekblue', text: '修炼体系', icon: <BookOutlined /> },\n    factions: { color: 'lime', text: '势力组织', icon: <TeamOutlined /> },\n    economy: { color: 'green', text: '经济制度', icon: <BankOutlined /> },\n    other: { color: 'default', text: '其他', icon: <GlobalOutlined /> }\n  };\n\n  // 表格列配置\n  const columns = [\n    {\n      title: '设定名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          {categoryConfig[record.category].icon}\n          <Text strong>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '类型',\n      dataIndex: 'category',\n      key: 'category',\n      render: (category) => (\n        <Tag color={categoryConfig[category].color}>\n          {categoryConfig[category].text}\n        </Tag>\n      ),\n      filters: Object.keys(categoryConfig).map(key => ({\n        text: categoryConfig[key].text,\n        value: key\n      })),\n      onFilter: (value, record) => record.category === value\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      ellipsis: true\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={status === 'active' ? 'green' : 'orange'}>\n          {status === 'active' ? '已完成' : '草稿'}\n        </Tag>\n      )\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerate(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个设定吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理新建/编辑设定\n  const handleCreateOrEdit = () => {\n    setEditingSetting(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (setting) => {\n    setEditingSetting(setting);\n    form.setFieldsValue({\n      ...setting,\n      ...setting.content\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (setting) => {\n    setViewingSetting(setting);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (setting) => {\n    message.info(`AI生成世界设定：${setting.name}`);\n  };\n\n  const handleDelete = (id) => {\n    setSettings(settings.filter(s => s.id !== id));\n    message.success('设定删除成功');\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 分离基本信息和内容\n      const { name, category, description, status, ...content } = values;\n      const processedValues = {\n        name,\n        category,\n        description,\n        status,\n        content\n      };\n\n      if (editingSetting) {\n        // 编辑设定\n        setSettings(settings.map(s =>\n          s.id === editingSetting.id\n            ? { ...s, ...processedValues, updatedAt: new Date().toISOString().split('T')[0] }\n            : s\n        ));\n        message.success('设定更新成功');\n      } else {\n        // 新建设定\n        const newSetting = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setSettings([...settings, newSetting]);\n        message.success('设定创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalSettings = settings.length;\n  const activeSettings = settings.filter(s => s.status === 'active').length;\n  const draftSettings = settings.filter(s => s.status === 'draft').length;\n  const categoryStats = Object.keys(categoryConfig).reduce((acc, key) => {\n    acc[key] = settings.filter(s => s.category === key).length;\n    return acc;\n  }, {});\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">世界设定</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总设定数\"\n              value={totalSettings}\n              prefix={<GlobalOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={activeSettings}\n              prefix={<BookOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"草稿\"\n              value={draftSettings}\n              prefix={<EditOutlined style={{ color: '#faad14' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"修炼体系\"\n              value={categoryStats.cultivation || 0}\n              prefix={<BookOutlined style={{ color: '#722ed1' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加设定\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={settings}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个设定`\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑设定模态框 */}\n      <Modal\n        title={editingSetting ? '编辑世界设定' : '新建世界设定'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={1000}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            category: 'world_basic',\n            status: 'draft'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"设定名称\"\n                rules={[{ required: true, message: '请输入设定名称' }]}\n              >\n                <Input placeholder=\"请输入设定名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"category\"\n                label=\"设定类型\"\n                rules={[{ required: true, message: '请选择设定类型' }]}\n              >\n                <Select>\n                  {Object.keys(categoryConfig).map(key => (\n                    <Option key={key} value={key}>\n                      {categoryConfig[key].text}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"设定描述\"\n            rules={[{ required: true, message: '请输入设定描述' }]}\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"简要描述这个设定的内容和作用\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择状态' }]}\n          >\n            <Select>\n              <Option value=\"draft\">草稿</Option>\n              <Option value=\"active\">已完成</Option>\n            </Select>\n          </Form.Item>\n\n          <Tabs defaultActiveKey=\"basic\">\n            <TabPane tab=\"基础信息\" key=\"basic\">\n              <Form.Item name=\"worldName\" label=\"世界名称\">\n                <Input placeholder=\"如：九州大陆、修仙界等\" />\n              </Form.Item>\n\n              <Form.Item name=\"overview\" label=\"世界概述\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的整体情况和特色...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"地理环境\" key=\"geography\">\n              <Form.Item name=\"geography\" label=\"地理描述\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的地理环境、地形地貌...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"climate\" label=\"气候环境\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述气候特点、季节变化...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"历史文化\" key=\"history\">\n              <Form.Item name=\"history\" label=\"历史背景\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的历史发展、重大事件...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"culture\" label=\"文化特色\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述文化传统、价值观念...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"政治经济\" key=\"politics\">\n              <Form.Item name=\"politics\" label=\"政治体系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述政治制度、权力结构...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"economy\" label=\"经济制度\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述经济体系、货币制度...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"货币体系\" key=\"currency\">\n              <Form.Item name=\"currencyName\" label=\"主要货币\">\n                <Input placeholder=\"如：灵石、金币等\" />\n              </Form.Item>\n\n              <Form.Item name=\"currencySystem\" label=\"货币制度\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述货币制度、汇率体系...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"monetaryPolicy\" label=\"货币政策\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述发行机构、监管制度...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"商业体系\" key=\"commerce\">\n              <Form.Item name=\"tradeRoutes\" label=\"贸易路线\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述主要贸易路线、商业网络...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"guilds\" label=\"商会组织\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述商会、公会等商业组织...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"marketRules\" label=\"市场规则\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述市场机制、商业法规...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"种族类别\" key=\"races\">\n              <Form.Item name=\"majorRaces\" label=\"主要种族\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述世界中的主要种族...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"raceRelations\" label=\"种族关系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述种族间的关系、冲突...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"raceTraits\" label=\"种族特征\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述各种族的特殊能力、文化...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"功法体系\" key=\"martial_arts\">\n              <Form.Item name=\"techniqueTypes\" label=\"功法分类\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述功法的分类、等级...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"cultivationMethods\" label=\"修炼方法\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述修炼方式、资源需求...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"martialArtsRules\" label=\"功法规则\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述功法的限制、禁忌...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"装备体系\" key=\"equipment\">\n              <Form.Item name=\"equipmentTypes\" label=\"装备分类\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述装备的类型、品级...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"enhancementSystem\" label=\"强化系统\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述装备强化、升级机制...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"craftingSystem\" label=\"制作系统\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述装备制作、材料需求...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"宠物体系\" key=\"pets\">\n              <Form.Item name=\"petTypes\" label=\"宠物分类\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述宠物的种类、稀有度...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"petEvolution\" label=\"进化系统\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述宠物进化、成长机制...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"petSkills\" label=\"技能系统\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述宠物技能、培养方式...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"地图结构\" key=\"maps\">\n              <Form.Item name=\"mapHierarchy\" label=\"地图层级\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述地图的层级结构...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"terrainTypes\" label=\"地形类型\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述各种地形特征...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"specialAreas\" label=\"特殊区域\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述特殊地点、秘境...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"维度结构\" key=\"dimensions\">\n              <Form.Item name=\"dimensionTypes\" label=\"维度类型\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述不同维度的特征...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"dimensionLaws\" label=\"维度法则\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述各维度的物理法则...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"dimensionTravel\" label=\"维度穿越\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述维度间的旅行方式...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"灵宝体系\" key=\"spiritual_treasures\">\n              <Form.Item name=\"treasureGrades\" label=\"灵宝品级\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述灵宝的品级划分...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"spiritSystem\" label=\"器灵系统\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述器灵的觉醒、能力...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"refiningMethods\" label=\"炼制方法\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述灵宝的炼制、升级...\"\n                />\n              </Form.Item>\n            </TabPane>\n\n            <TabPane tab=\"特殊规则\" key=\"rules\">\n              <Form.Item name=\"rules\" label=\"世界规则\">\n                <TextArea\n                  rows={4}\n                  placeholder=\"描述世界的特殊规则、法则...\"\n                />\n              </Form.Item>\n\n              <Form.Item name=\"magic\" label=\"力量体系\">\n                <TextArea\n                  rows={3}\n                  placeholder=\"描述修炼、魔法等力量体系...\"\n                />\n              </Form.Item>\n            </TabPane>\n          </Tabs>\n        </Form>\n      </Modal>\n\n      {/* 设定详情模态框 */}\n      <Modal\n        title=\"世界设定详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={1000}\n      >\n        {viewingSetting && (\n          <div>\n            <Row gutter={16} style={{ marginBottom: 16 }}>\n              <Col span={24}>\n                <Title level={3}>{viewingSetting.name}</Title>\n                <Space>\n                  <Tag color={categoryConfig[viewingSetting.category].color}>\n                    {categoryConfig[viewingSetting.category].text}\n                  </Tag>\n                  <Tag color={viewingSetting.status === 'active' ? 'green' : 'orange'}>\n                    {viewingSetting.status === 'active' ? '已完成' : '草稿'}\n                  </Tag>\n                </Space>\n                <Paragraph style={{ marginTop: 16 }}>\n                  {viewingSetting.description}\n                </Paragraph>\n              </Col>\n            </Row>\n\n            <Collapse defaultActiveKey={['1']}>\n              {viewingSetting.content.worldName && (\n                <Panel header=\"世界名称\" key=\"1\">\n                  <Text>{viewingSetting.content.worldName}</Text>\n                </Panel>\n              )}\n\n              {viewingSetting.content.geography && (\n                <Panel header=\"地理环境\" key=\"2\">\n                  <Paragraph>{viewingSetting.content.geography}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.history && (\n                <Panel header=\"历史背景\" key=\"3\">\n                  <Paragraph>{viewingSetting.content.history}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.politics && (\n                <Panel header=\"政治体系\" key=\"4\">\n                  <Paragraph>{viewingSetting.content.politics}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.economy && (\n                <Panel header=\"经济制度\" key=\"5\">\n                  <Paragraph>{viewingSetting.content.economy}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.culture && (\n                <Panel header=\"文化特色\" key=\"6\">\n                  <Paragraph>{viewingSetting.content.culture}</Paragraph>\n                </Panel>\n              )}\n\n              {viewingSetting.content.currencySystem && (\n                <Panel header=\"货币体系\" key=\"7\">\n                  <Paragraph>{viewingSetting.content.currencySystem}</Paragraph>\n                  {viewingSetting.content.currencyName && (\n                    <Text strong>主要货币：{viewingSetting.content.currencyName}</Text>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.tradeRoutes && (\n                <Panel header=\"商业体系\" key=\"8\">\n                  <Paragraph>{viewingSetting.content.tradeRoutes}</Paragraph>\n                  {viewingSetting.content.guilds && (\n                    <Paragraph><Text strong>商会组织：</Text>{viewingSetting.content.guilds}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.majorRaces && (\n                <Panel header=\"种族类别\" key=\"9\">\n                  <Paragraph>{viewingSetting.content.majorRaces}</Paragraph>\n                  {viewingSetting.content.raceRelations && (\n                    <Paragraph><Text strong>种族关系：</Text>{viewingSetting.content.raceRelations}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.techniqueTypes && (\n                <Panel header=\"功法体系\" key=\"10\">\n                  <Paragraph>{viewingSetting.content.techniqueTypes}</Paragraph>\n                  {viewingSetting.content.cultivationMethods && (\n                    <Paragraph><Text strong>修炼方法：</Text>{viewingSetting.content.cultivationMethods}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.equipmentTypes && (\n                <Panel header=\"装备体系\" key=\"11\">\n                  <Paragraph>{viewingSetting.content.equipmentTypes}</Paragraph>\n                  {viewingSetting.content.enhancementSystem && (\n                    <Paragraph><Text strong>强化系统：</Text>{viewingSetting.content.enhancementSystem}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.petTypes && (\n                <Panel header=\"宠物体系\" key=\"12\">\n                  <Paragraph>{viewingSetting.content.petTypes}</Paragraph>\n                  {viewingSetting.content.petEvolution && (\n                    <Paragraph><Text strong>进化系统：</Text>{viewingSetting.content.petEvolution}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.mapHierarchy && (\n                <Panel header=\"地图结构\" key=\"13\">\n                  <Paragraph>{viewingSetting.content.mapHierarchy}</Paragraph>\n                  {viewingSetting.content.terrainTypes && (\n                    <Paragraph><Text strong>地形类型：</Text>{viewingSetting.content.terrainTypes}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.dimensionTypes && (\n                <Panel header=\"维度结构\" key=\"14\">\n                  <Paragraph>{viewingSetting.content.dimensionTypes}</Paragraph>\n                  {viewingSetting.content.dimensionLaws && (\n                    <Paragraph><Text strong>维度法则：</Text>{viewingSetting.content.dimensionLaws}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.treasureGrades && (\n                <Panel header=\"灵宝体系\" key=\"15\">\n                  <Paragraph>{viewingSetting.content.treasureGrades}</Paragraph>\n                  {viewingSetting.content.spiritSystem && (\n                    <Paragraph><Text strong>器灵系统：</Text>{viewingSetting.content.spiritSystem}</Paragraph>\n                  )}\n                </Panel>\n              )}\n\n              {viewingSetting.content.rules && (\n                <Panel header=\"世界规则\" key=\"16\">\n                  <Paragraph>{viewingSetting.content.rules}</Paragraph>\n                </Panel>\n              )}\n            </Collapse>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorldSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,IAAI,EACJC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,mBAAmB,EACnBC,eAAe,EACfC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAG1C,UAAU;AAC7C,MAAM;EAAE2C;AAAS,CAAC,GAAGpC,KAAK;AAC1B,MAAM;EAAEqC;AAAO,CAAC,GAAGpC,MAAM;AACzB,MAAM;EAAEqC;AAAQ,CAAC,GAAG7B,IAAI;AACxB,MAAM;EAAE8B;AAAM,CAAC,GAAG7B,QAAQ;AAE1B,MAAM8B,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8D,cAAc,EAAEC,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgE,IAAI,CAAC,GAAGvD,IAAI,CAACwD,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,UAAU;IAChBC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,eAAe;IAC5BC,OAAO,EAAE;MACPC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,4BAA4B;MACvCC,OAAO,EAAE,2BAA2B;MACpCC,QAAQ,EAAE,yBAAyB;MACnCC,OAAO,EAAE,mBAAmB;MAC5BC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,aAAa;IACvBC,WAAW,EAAE,YAAY;IACzBC,OAAO,EAAE;MACPU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAChEC,OAAO,EAAE,sBAAsB;MAC/BC,SAAS,EAAE,gBAAgB;MAC3BC,WAAW,EAAE,aAAa;MAC1BC,QAAQ,EAAE;IACZ,CAAC;IACDP,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,cAAc;IAC3BC,OAAO,EAAE;MACPe,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MACxCC,aAAa,EAAE,aAAa;MAC5BC,WAAW,EAAE,WAAW;MACxBL,SAAS,EAAE,WAAW;MACtBM,SAAS,EAAE;IACb,CAAC;IACDX,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED/E,SAAS,CAAC,MAAM;IACdoD,WAAW,CAACa,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwB,cAAc,GAAG;IACrBC,WAAW,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACpB,cAAc;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACtEzB,SAAS,EAAE;MAAEmB,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACf,mBAAmB;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC1ExB,OAAO,EAAE;MAAEkB,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACd,eAAe;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACrEvB,QAAQ,EAAE;MAAEiB,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACb,YAAY;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAChEC,QAAQ,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACV,cAAc;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACnEE,QAAQ,EAAE;MAAER,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACT,YAAY;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEG,KAAK,EAAE;MAAET,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACR,oBAAoB;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACzEI,YAAY,EAAE;MAAEV,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACP,mBAAmB;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC/EK,SAAS,EAAE;MAAEX,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACN,YAAY;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACpEM,IAAI,EAAE;MAAEZ,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACL,aAAa;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC9DO,IAAI,EAAE;MAAEb,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACJ,eAAe;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAChEQ,UAAU,EAAE;MAAEd,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACH,eAAe;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACxES,mBAAmB,EAAE;MAAEf,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACF,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC5EU,WAAW,EAAE;MAAEhB,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACX,YAAY;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACxEW,QAAQ,EAAE;MAAEjB,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACZ,YAAY;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEtB,OAAO,EAAE;MAAEgB,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAEpD,OAAA,CAACb,YAAY;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEY,KAAK,EAAE;MAAElB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAEpD,OAAA,CAACpB,cAAc;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EAClE,CAAC;;EAED;EACA,MAAMa,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACtB,IAAI,EAAEuB,MAAM,kBACnB1E,OAAA,CAACpC,KAAK;MAAA+G,QAAA,GACH3B,cAAc,CAAC0B,MAAM,CAAC/C,QAAQ,CAAC,CAACyB,IAAI,eACrCpD,OAAA,CAACE,IAAI;QAAC0E,MAAM;QAAAD,QAAA,EAAExB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAEX,CAAC,EACD;IACEc,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAG9C,QAAQ,iBACf3B,OAAA,CAACnC,GAAG;MAACqF,KAAK,EAAEF,cAAc,CAACrB,QAAQ,CAAC,CAACuB,KAAM;MAAAyB,QAAA,EACxC3B,cAAc,CAACrB,QAAQ,CAAC,CAACwB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACN;IACDqB,OAAO,EAAEC,MAAM,CAACC,IAAI,CAAC/B,cAAc,CAAC,CAACgC,GAAG,CAACR,GAAG,KAAK;MAC/CrB,IAAI,EAAEH,cAAc,CAACwB,GAAG,CAAC,CAACrB,IAAI;MAC9B8B,KAAK,EAAET;IACT,CAAC,CAAC,CAAC;IACHU,QAAQ,EAAEA,CAACD,KAAK,EAAEP,MAAM,KAAKA,MAAM,CAAC/C,QAAQ,KAAKsD;EACnD,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBW,QAAQ,EAAE;EACZ,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGrC,MAAM,iBACbpC,OAAA,CAACnC,GAAG;MAACqF,KAAK,EAAEd,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAS;MAAAuC,QAAA,EAClDvC,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG;IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC;EAET,CAAC,EACD;IACEc,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAAC/C,SAAS,CAAC,GAAG,IAAIiD,IAAI,CAACD,CAAC,CAAChD,SAAS;EAChE,CAAC,EACD;IACEgC,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEd,MAAM,kBAChB1E,OAAA,CAACpC,KAAK;MAAA+G,QAAA,gBACJ3E,OAAA,CAAC5B,OAAO;QAACkG,KAAK,EAAC,0BAAM;QAAAK,QAAA,eACnB3E,OAAA,CAACtC,MAAM;UACL+H,IAAI,EAAC,MAAM;UACXrC,IAAI,eAAEpD,OAAA,CAACjB,WAAW;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBkC,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACjB,MAAM;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAAC5B,OAAO;QAACkG,KAAK,EAAC,cAAI;QAAAK,QAAA,eACjB3E,OAAA,CAACtC,MAAM;UACL+H,IAAI,EAAC,MAAM;UACXrC,IAAI,eAAEpD,OAAA,CAACnB,YAAY;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBkC,OAAO,EAAEA,CAAA,KAAME,UAAU,CAAClB,MAAM;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAAC5B,OAAO;QAACkG,KAAK,EAAC,gBAAM;QAAAK,QAAA,eACnB3E,OAAA,CAACtC,MAAM;UACL+H,IAAI,EAAC,MAAM;UACXrC,IAAI,eAAEpD,OAAA,CAAChB,aAAa;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAACnB,MAAM;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVxD,OAAA,CAAC7B,UAAU;QACTmG,KAAK,EAAC,8DAAY;QAClBwB,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACrB,MAAM,CAACjD,EAAE,CAAE;QACzCuE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAtB,QAAA,eAEf3E,OAAA,CAAC5B,OAAO;UAACkG,KAAK,EAAC,cAAI;UAAAK,QAAA,eACjB3E,OAAA,CAACtC,MAAM;YACL+H,IAAI,EAAC,MAAM;YACXS,MAAM;YACN9C,IAAI,eAAEpD,OAAA,CAAClB,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAM2C,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhF,iBAAiB,CAAC,IAAI,CAAC;IACvBG,IAAI,CAAC8E,WAAW,CAAC,CAAC;IAClBrF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM6E,UAAU,GAAIS,OAAO,IAAK;IAC9BlF,iBAAiB,CAACkF,OAAO,CAAC;IAC1B/E,IAAI,CAACgF,cAAc,CAAC;MAClB,GAAGD,OAAO;MACV,GAAGA,OAAO,CAACxE;IACb,CAAC,CAAC;IACFd,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4E,UAAU,GAAIU,OAAO,IAAK;IAC9BhF,iBAAiB,CAACgF,OAAO,CAAC;IAC1BpF,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM4E,gBAAgB,GAAIQ,OAAO,IAAK;IACpCnI,OAAO,CAACqI,IAAI,CAAC,YAAYF,OAAO,CAAC3E,IAAI,EAAE,CAAC;EAC1C,CAAC;EAED,MAAMqE,YAAY,GAAItE,EAAE,IAAK;IAC3Bd,WAAW,CAACD,QAAQ,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChF,EAAE,KAAKA,EAAE,CAAC,CAAC;IAC9CvD,OAAO,CAACwI,OAAO,CAAC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMtF,IAAI,CAACuF,cAAc,CAAC,CAAC;MAC1ChG,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM;QAAEa,IAAI;QAAEC,QAAQ;QAAEC,WAAW;QAAEQ,MAAM;QAAE,GAAGP;MAAQ,CAAC,GAAG+E,MAAM;MAClE,MAAME,eAAe,GAAG;QACtBpF,IAAI;QACJC,QAAQ;QACRC,WAAW;QACXQ,MAAM;QACNP;MACF,CAAC;MAED,IAAIX,cAAc,EAAE;QAClB;QACAP,WAAW,CAACD,QAAQ,CAACsE,GAAG,CAACyB,CAAC,IACxBA,CAAC,CAAChF,EAAE,KAAKP,cAAc,CAACO,EAAE,GACtB;UAAE,GAAGgF,CAAC;UAAE,GAAGK,eAAe;UAAExE,SAAS,EAAE,IAAIiD,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GAC/EP,CACN,CAAC,CAAC;QACFvI,OAAO,CAACwI,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMO,UAAU,GAAG;UACjBxF,EAAE,EAAE8D,IAAI,CAAC2B,GAAG,CAAC,CAAC;UACd,GAAGJ,eAAe;UAClBzE,SAAS,EAAE,IAAIkD,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD1E,SAAS,EAAE,IAAIiD,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDrG,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEuG,UAAU,CAAC,CAAC;QACtC/I,OAAO,CAACwI,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA3F,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAAC8E,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRtG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwG,aAAa,GAAG3G,QAAQ,CAAC4G,MAAM;EACrC,MAAMC,cAAc,GAAG7G,QAAQ,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrE,MAAM,KAAK,QAAQ,CAAC,CAACkF,MAAM;EACzE,MAAME,aAAa,GAAG9G,QAAQ,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrE,MAAM,KAAK,OAAO,CAAC,CAACkF,MAAM;EACvE,MAAMG,aAAa,GAAG3C,MAAM,CAACC,IAAI,CAAC/B,cAAc,CAAC,CAAC0E,MAAM,CAAC,CAACC,GAAG,EAAEnD,GAAG,KAAK;IACrEmD,GAAG,CAACnD,GAAG,CAAC,GAAG9D,QAAQ,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9E,QAAQ,KAAK6C,GAAG,CAAC,CAAC8C,MAAM;IAC1D,OAAOK,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,oBACE3H,OAAA;IAAK4H,SAAS,EAAC,SAAS;IAAAjD,QAAA,gBACtB3E,OAAA;MAAK4H,SAAS,EAAC,aAAa;MAAAjD,QAAA,eAC1B3E,OAAA,CAACC,KAAK;QAAC4H,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAjD,QAAA,EAAC;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNxD,OAAA,CAAC3B,GAAG;MAACyJ,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArD,QAAA,gBAC3C3E,OAAA,CAAC1B,GAAG;QAAC2J,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACX3E,OAAA,CAACxC,IAAI;UAAAmH,QAAA,eACH3E,OAAA,CAACzB,SAAS;YACR+F,KAAK,EAAC,0BAAM;YACZW,KAAK,EAAEoC,aAAc;YACrBa,MAAM,eAAElI,OAAA,CAACpB,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAAC1B,GAAG;QAAC2J,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACX3E,OAAA,CAACxC,IAAI;UAAAmH,QAAA,eACH3E,OAAA,CAACzB,SAAS;YACR+F,KAAK,EAAC,oBAAK;YACXW,KAAK,EAAEsC,cAAe;YACtBW,MAAM,eAAElI,OAAA,CAACX,YAAY;cAAC0I,KAAK,EAAE;gBAAE7E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAAC1B,GAAG;QAAC2J,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACX3E,OAAA,CAACxC,IAAI;UAAAmH,QAAA,eACH3E,OAAA,CAACzB,SAAS;YACR+F,KAAK,EAAC,cAAI;YACVW,KAAK,EAAEuC,aAAc;YACrBU,MAAM,eAAElI,OAAA,CAACnB,YAAY;cAACkJ,KAAK,EAAE;gBAAE7E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxD,OAAA,CAAC1B,GAAG;QAAC2J,IAAI,EAAE,CAAE;QAAAtD,QAAA,eACX3E,OAAA,CAACxC,IAAI;UAAAmH,QAAA,eACH3E,OAAA,CAACzB,SAAS;YACR+F,KAAK,EAAC,0BAAM;YACZW,KAAK,EAAEwC,aAAa,CAACvD,WAAW,IAAI,CAAE;YACtCgE,MAAM,eAAElI,OAAA,CAACX,YAAY;cAAC0I,KAAK,EAAE;gBAAE7E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxD,OAAA,CAACxC,IAAI;MAAAmH,QAAA,gBACH3E,OAAA;QAAK4H,SAAS,EAAC,SAAS;QAAAjD,QAAA,eACtB3E,OAAA;UAAK4H,SAAS,EAAC,cAAc;UAAAjD,QAAA,eAC3B3E,OAAA,CAACtC,MAAM;YACL+H,IAAI,EAAC,SAAS;YACdrC,IAAI,eAAEpD,OAAA,CAACrB,YAAY;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBkC,OAAO,EAAES,kBAAmB;YAAAxB,QAAA,EAC7B;UAED;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxD,OAAA,CAACrC,KAAK;QACJ0G,OAAO,EAAEA,OAAQ;QACjB8D,UAAU,EAAEzH,QAAS;QACrB0H,MAAM,EAAC,IAAI;QACXxH,OAAO,EAAEA,OAAQ;QACjByH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPxD,OAAA,CAAClC,KAAK;MACJwG,KAAK,EAAEpD,cAAc,GAAG,QAAQ,GAAG,QAAS;MAC5CwH,IAAI,EAAE5H,YAAa;MACnB6H,IAAI,EAAEhC,aAAc;MACpBiC,QAAQ,EAAEA,CAAA,KAAM;QACd7H,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAAC8E,WAAW,CAAC,CAAC;MACpB,CAAE;MACFyC,cAAc,EAAEjI,OAAQ;MACxBkI,KAAK,EAAE,IAAK;MAAAnE,QAAA,eAEZ3E,OAAA,CAACjC,IAAI;QACHuD,IAAI,EAAEA,IAAK;QACXyH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbrH,QAAQ,EAAE,aAAa;UACvBS,MAAM,EAAE;QACV,CAAE;QAAAuC,QAAA,gBAEF3E,OAAA,CAAC3B,GAAG;UAACyJ,MAAM,EAAE,EAAG;UAAAnD,QAAA,gBACd3E,OAAA,CAAC1B,GAAG;YAAC2J,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cACRvH,IAAI,EAAC,MAAM;cACXwH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD3E,OAAA,CAAChC,KAAK;gBAACqL,WAAW,EAAC;cAAS;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNxD,OAAA,CAAC1B,GAAG;YAAC2J,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cACRvH,IAAI,EAAC,UAAU;cACfwH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAElL,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD3E,OAAA,CAAC/B,MAAM;gBAAA0G,QAAA,EACJG,MAAM,CAACC,IAAI,CAAC/B,cAAc,CAAC,CAACgC,GAAG,CAACR,GAAG,iBAClCxE,OAAA,CAACK,MAAM;kBAAW4E,KAAK,EAAET,GAAI;kBAAAG,QAAA,EAC1B3B,cAAc,CAACwB,GAAG,CAAC,CAACrB;gBAAI,GADdqB,GAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAER,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;UACRvH,IAAI,EAAC,aAAa;UAClBwH,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElL,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyG,QAAA,eAEhD3E,OAAA,CAACI,QAAQ;YACPkJ,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAgB;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;UACRvH,IAAI,EAAC,QAAQ;UACbwH,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElL,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAyG,QAAA,eAE9C3E,OAAA,CAAC/B,MAAM;YAAA0G,QAAA,gBACL3E,OAAA,CAACK,MAAM;cAAC4E,KAAK,EAAC,OAAO;cAAAN,QAAA,EAAC;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCxD,OAAA,CAACK,MAAM;cAAC4E,KAAK,EAAC,QAAQ;cAAAN,QAAA,EAAC;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZxD,OAAA,CAACvB,IAAI;UAAC8K,gBAAgB,EAAC,OAAO;UAAA5E,QAAA,gBAC5B3E,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,WAAW;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACtC3E,OAAA,CAAChC,KAAK;gBAACqL,WAAW,EAAC;cAAa;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,UAAU;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACrC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAVU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWtB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,WAAW;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACtC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAmB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,SAAS;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,WAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAc1B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,SAAS;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAmB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,SAAS;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,SAAS;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcxB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,UAAU;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACrC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,SAAS;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACpC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAczB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAAChC,KAAK;gBAACqL,WAAW,EAAC;cAAU;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAjBU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBzB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,aAAa;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACxC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAkB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,QAAQ;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACnC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,aAAa;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACxC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBzB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,YAAY;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACvC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,eAAe;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC1C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,YAAY;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACvC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAkB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBtB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,oBAAoB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC/C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,kBAAkB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC7C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,cAAc;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqB7B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,mBAAmB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC9C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,WAAW;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqB1B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,UAAU;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACrC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,WAAW;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACtC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAgB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBrB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAc;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAa;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAc;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,MAAM;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBrB,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAc;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,eAAe;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC1C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,iBAAiB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC5C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,YAAY;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqB3B,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,gBAAgB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC3C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAc;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,cAAc;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eACzC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,iBAAiB;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAC5C3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAe;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GApBU,qBAAqB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqBpC,CAAC,eAEVxD,OAAA,CAACM,OAAO;YAACkJ,GAAG,EAAC,0BAAM;YAAA7E,QAAA,gBACjB3E,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,OAAO;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAClC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZxD,OAAA,CAACjC,IAAI,CAACkL,IAAI;cAACvH,IAAI,EAAC,OAAO;cAACwH,KAAK,EAAC,0BAAM;cAAAvE,QAAA,eAClC3E,OAAA,CAACI,QAAQ;gBACPkJ,IAAI,EAAE,CAAE;gBACRD,WAAW,EAAC;cAAiB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,GAbU,OAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OActB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRxD,OAAA,CAAClC,KAAK;MACJwG,KAAK,EAAC,sCAAQ;MACdoE,IAAI,EAAE1H,kBAAmB;MACzB4H,QAAQ,EAAEA,CAAA,KAAM3H,qBAAqB,CAAC,KAAK,CAAE;MAC7CwI,MAAM,EAAE,cACNzJ,OAAA,CAACtC,MAAM;QAAagI,OAAO,EAAEA,CAAA,KAAMzE,qBAAqB,CAAC,KAAK,CAAE;QAAA0D,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFsF,KAAK,EAAE,IAAK;MAAAnE,QAAA,EAEXvD,cAAc,iBACbpB,OAAA;QAAA2E,QAAA,gBACE3E,OAAA,CAAC3B,GAAG;UAACyJ,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAArD,QAAA,eAC3C3E,OAAA,CAAC1B,GAAG;YAAC2J,IAAI,EAAE,EAAG;YAAAtD,QAAA,gBACZ3E,OAAA,CAACC,KAAK;cAAC4H,KAAK,EAAE,CAAE;cAAAlD,QAAA,EAAEvD,cAAc,CAACM;YAAI;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9CxD,OAAA,CAACpC,KAAK;cAAA+G,QAAA,gBACJ3E,OAAA,CAACnC,GAAG;gBAACqF,KAAK,EAAEF,cAAc,CAAC5B,cAAc,CAACO,QAAQ,CAAC,CAACuB,KAAM;gBAAAyB,QAAA,EACvD3B,cAAc,CAAC5B,cAAc,CAACO,QAAQ,CAAC,CAACwB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNxD,OAAA,CAACnC,GAAG;gBAACqF,KAAK,EAAE9B,cAAc,CAACgB,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,QAAS;gBAAAuC,QAAA,EACjEvD,cAAc,CAACgB,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG;cAAI;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACRxD,OAAA,CAACG,SAAS;cAAC4H,KAAK,EAAE;gBAAE2B,SAAS,EAAE;cAAG,CAAE;cAAA/E,QAAA,EACjCvD,cAAc,CAACQ;YAAW;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxD,OAAA,CAACtB,QAAQ;UAAC6K,gBAAgB,EAAE,CAAC,GAAG,CAAE;UAAA5E,QAAA,GAC/BvD,cAAc,CAACS,OAAO,CAACC,SAAS,iBAC/B9B,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACE,IAAI;cAAAyE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACC;YAAS;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GADxB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACE,SAAS,iBAC/B/B,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACE;YAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADlC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACG,OAAO,iBAC7BhC,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACG;YAAO;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACI,QAAQ,iBAC9BjC,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACI;YAAQ;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADjC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACK,OAAO,iBAC7BlC,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACK;YAAO;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACM,OAAO,iBAC7BnC,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACM;YAAO;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADhC,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAErB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC+H,cAAc,iBACpC5J,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAAC+H;YAAc;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAACgI,YAAY,iBAClC7J,OAAA,CAACE,IAAI;cAAC0E,MAAM;cAAAD,QAAA,GAAC,gCAAK,EAACvD,cAAc,CAACS,OAAO,CAACgI,YAAY;YAAA;cAAAxG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAC9D;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACiI,WAAW,iBACjC9J,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACiI;YAAW;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC1DpC,cAAc,CAACS,OAAO,CAACkI,MAAM,iBAC5B/J,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACkI,MAAM;YAAA;cAAA1G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC/E;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACmI,UAAU,iBAChChK,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACmI;YAAU;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACzDpC,cAAc,CAACS,OAAO,CAACoI,aAAa,iBACnCjK,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACoI,aAAa;YAAA;cAAA5G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACtF;UAAA,GAJsB,GAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKrB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACqI,cAAc,iBACpClK,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACqI;YAAc;cAAA7G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAACsI,kBAAkB,iBACxCnK,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACsI,kBAAkB;YAAA;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC3F;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACuI,cAAc,iBACpCpK,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACuI;YAAc;cAAA/G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAACwI,iBAAiB,iBACvCrK,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACwI,iBAAiB;YAAA;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAC1F;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACyI,QAAQ,iBAC9BtK,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACyI;YAAQ;cAAAjH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACvDpC,cAAc,CAACS,OAAO,CAAC0I,YAAY,iBAClCvK,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAAC0I,YAAY;YAAA;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACrF;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC2I,YAAY,iBAClCxK,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAAC2I;YAAY;cAAAnH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC3DpC,cAAc,CAACS,OAAO,CAAC4I,YAAY,iBAClCzK,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAAC4I,YAAY;YAAA;cAAApH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACrF;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC6I,cAAc,iBACpC1K,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAAC6I;YAAc;cAAArH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAAC8I,aAAa,iBACnC3K,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAAC8I,aAAa;YAAA;cAAAtH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACtF;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAAC+I,cAAc,iBACpC5K,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,gBAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAAC+I;YAAc;cAAAvH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAC7DpC,cAAc,CAACS,OAAO,CAACgJ,YAAY,iBAClC7K,OAAA,CAACG,SAAS;cAAAwE,QAAA,gBAAC3E,OAAA,CAACE,IAAI;gBAAC0E,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAACpC,cAAc,CAACS,OAAO,CAACgJ,YAAY;YAAA;cAAAxH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACrF;UAAA,GAJsB,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtB,CACR,EAEApC,cAAc,CAACS,OAAO,CAACsH,KAAK,iBAC3BnJ,OAAA,CAACO,KAAK;YAACoJ,MAAM,EAAC,0BAAM;YAAAhF,QAAA,eAClB3E,OAAA,CAACG,SAAS;cAAAwE,QAAA,EAAEvD,cAAc,CAACS,OAAO,CAACsH;YAAK;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GAD9B,IAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEtB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAn1BID,aAAa;EAAA,QAOFzC,IAAI,CAACwD,OAAO;AAAA;AAAAuJ,EAAA,GAPvBtK,aAAa;AAq1BnB,eAAeA,aAAa;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}