{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ResourceDistribution.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Rate, Progress } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EnvironmentOutlined, GoldOutlined, ThunderboltOutlined, FireOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ResourceDistribution = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [resources, setResources] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingResource, setEditingResource] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockResources = [{\n    id: 1,\n    name: '灵石矿脉',\n    type: 'mineral',\n    location: '青云山脉',\n    coordinates: {\n      x: 120,\n      y: 80\n    },\n    rarity: 'rare',\n    difficulty: 4,\n    economicValue: 5,\n    description: '蕴含丰富灵气的矿脉，是修炼者必需的资源',\n    extractionMethod: '需要筑基期以上修为才能安全开采',\n    renewalRate: 'slow',\n    controllingFaction: '青云宗',\n    status: 'active'\n  }, {\n    id: 2,\n    name: '千年雪莲',\n    type: 'herb',\n    location: '雪域高原',\n    coordinates: {\n      x: 200,\n      y: 150\n    },\n    rarity: 'legendary',\n    difficulty: 5,\n    economicValue: 5,\n    description: '极其珍贵的炼丹材料，可延年益寿',\n    extractionMethod: '需要在特定时间采摘，且需要抵御严寒',\n    renewalRate: 'very_slow',\n    controllingFaction: '无',\n    status: 'protected'\n  }];\n  useEffect(() => {\n    loadResources();\n  }, [projectId]);\n  const loadResources = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setResources(mockResources);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载资源分布失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingResource(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = resource => {\n    setEditingResource(resource);\n    form.setFieldsValue(resource);\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 模拟API调用\n      setResources(resources.filter(r => r.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingResource) {\n        // 更新\n        setResources(resources.map(r => r.id === editingResource.id ? {\n          ...r,\n          ...values\n        } : r));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newResource = {\n          id: Date.now(),\n          ...values\n        };\n        setResources([...resources, newResource]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      mineral: 'gold',\n      herb: 'green',\n      water: 'blue',\n      energy: 'purple',\n      artifact: 'red'\n    };\n    return colors[type] || 'default';\n  };\n  const getRarityColor = rarity => {\n    const colors = {\n      common: 'default',\n      uncommon: 'blue',\n      rare: 'purple',\n      epic: 'orange',\n      legendary: 'red'\n    };\n    return colors[rarity] || 'default';\n  };\n  const getStatusColor = status => {\n    const colors = {\n      active: 'green',\n      depleted: 'red',\n      protected: 'blue',\n      contested: 'orange'\n    };\n    return colors[status] || 'default';\n  };\n  const columns = [{\n    title: '资源名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'mineral' ? '矿物' : record.type === 'herb' ? '草药' : record.type === 'water' ? '水源' : record.type === 'energy' ? '能量' : '神器'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location',\n    render: (text, record) => {\n      var _record$coordinates, _record$coordinates2;\n      return /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          children: text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"(\", (_record$coordinates = record.coordinates) === null || _record$coordinates === void 0 ? void 0 : _record$coordinates.x, \", \", (_record$coordinates2 = record.coordinates) === null || _record$coordinates2 === void 0 ? void 0 : _record$coordinates2.y, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this);\n    }\n  }, {\n    title: '稀有度',\n    dataIndex: 'rarity',\n    key: 'rarity',\n    render: rarity => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getRarityColor(rarity),\n      children: rarity === 'common' ? '普通' : rarity === 'uncommon' ? '不常见' : rarity === 'rare' ? '稀有' : rarity === 'epic' ? '史诗' : '传说'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '开采难度',\n    dataIndex: 'difficulty',\n    key: 'difficulty',\n    render: difficulty => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: difficulty,\n      style: {\n        fontSize: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '经济价值',\n    dataIndex: 'economicValue',\n    key: 'economicValue',\n    render: value => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(GoldOutlined, {\n        style: {\n          color: '#faad14'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Rate, {\n        disabled: true,\n        value: value,\n        style: {\n          fontSize: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '控制势力',\n    dataIndex: 'controllingFaction',\n    key: 'controllingFaction',\n    render: faction => faction || /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: \"\\u65E0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 39\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status === 'active' ? '活跃' : status === 'depleted' ? '枯竭' : status === 'protected' ? '受保护' : '争夺中'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u8D44\\u6E90\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), \" \\u8D44\\u6E90\\u5206\\u5E03\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u8D44\\u6E90\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(GoldOutlined, {\n              style: {\n                fontSize: 24,\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u603B\\u8D44\\u6E90\\u6570\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                },\n                children: resources.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {\n              style: {\n                fontSize: 24,\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6D3B\\u8DC3\\u8D44\\u6E90\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                },\n                children: resources.filter(r => r.status === 'active').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(FireOutlined, {\n              style: {\n                fontSize: 24,\n                color: '#f5222d'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u4E89\\u593A\\u8D44\\u6E90\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                },\n                children: resources.filter(r => r.status === 'contested').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(EnvironmentOutlined, {\n              style: {\n                fontSize: 24,\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u53D7\\u4FDD\\u62A4\\u8D44\\u6E90\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                },\n                children: resources.filter(r => r.status === 'protected').length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: resources,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个资源`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingResource ? '编辑资源' : '添加资源',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'mineral',\n          rarity: 'common',\n          difficulty: 1,\n          economicValue: 1,\n          renewalRate: 'medium',\n          status: 'active'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u8D44\\u6E90\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入资源名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D44\\u6E90\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u8D44\\u6E90\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择资源类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"mineral\",\n                  children: \"\\u77FF\\u7269\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"herb\",\n                  children: \"\\u8349\\u836F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"water\",\n                  children: \"\\u6C34\\u6E90\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"energy\",\n                  children: \"\\u80FD\\u91CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"artifact\",\n                  children: \"\\u795E\\u5668\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"location\",\n              label: \"\\u4F4D\\u7F6E\",\n              rules: [{\n                required: true,\n                message: '请输入位置'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F4D\\u7F6E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"controllingFaction\",\n              label: \"\\u63A7\\u5236\\u52BF\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u63A7\\u5236\\u52BF\\u529B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"rarity\",\n              label: \"\\u7A00\\u6709\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择稀有度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"common\",\n                  children: \"\\u666E\\u901A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"uncommon\",\n                  children: \"\\u4E0D\\u5E38\\u89C1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"rare\",\n                  children: \"\\u7A00\\u6709\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"epic\",\n                  children: \"\\u53F2\\u8BD7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"legendary\",\n                  children: \"\\u4F20\\u8BF4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"difficulty\",\n              label: \"\\u5F00\\u91C7\\u96BE\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择开采难度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"economicValue\",\n              label: \"\\u7ECF\\u6D4E\\u4EF7\\u503C\",\n              rules: [{\n                required: true,\n                message: '请选择经济价值'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"renewalRate\",\n              label: \"\\u518D\\u751F\\u901F\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择再生速度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"very_slow\",\n                  children: \"\\u6781\\u6162\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"slow\",\n                  children: \"\\u7F13\\u6162\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\\u7B49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"fast\",\n                  children: \"\\u5FEB\\u901F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"very_fast\",\n                  children: \"\\u6781\\u5FEB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u6D3B\\u8DC3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"depleted\",\n                  children: \"\\u67AF\\u7AED\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"protected\",\n                  children: \"\\u53D7\\u4FDD\\u62A4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"contested\",\n                  children: \"\\u4E89\\u593A\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u8D44\\u6E90\\u7684\\u7279\\u70B9\\u548C\\u7528\\u9014\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 507,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"extractionMethod\",\n          label: \"\\u5F00\\u91C7\\u65B9\\u6CD5\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u5F00\\u91C7\\u65B9\\u6CD5\\u548C\\u6CE8\\u610F\\u4E8B\\u9879\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 287,\n    columnNumber: 5\n  }, this);\n};\n_s(ResourceDistribution, \"QSFwL3ULWC/IUruqcN5tXiX5o2I=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = ResourceDistribution;\nexport default ResourceDistribution;\nvar _c;\n$RefreshReg$(_c, \"ResourceDistribution\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Rate", "Progress", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EnvironmentOutlined", "GoldOutlined", "ThunderboltOutlined", "FireOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "ResourceDistribution", "_s", "id", "projectId", "resources", "setResources", "loading", "setLoading", "modalVisible", "setModalVisible", "editingResource", "setEditingResource", "form", "useForm", "mockResources", "name", "type", "location", "coordinates", "x", "y", "rarity", "difficulty", "economicValue", "description", "extractionMethod", "renewalRate", "controllingFaction", "status", "loadResources", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "resource", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "filter", "r", "success", "handleSubmit", "values", "map", "newResource", "Date", "now", "getTypeColor", "colors", "mineral", "herb", "water", "energy", "artifact", "getRarityColor", "common", "uncommon", "rare", "epic", "legendary", "getStatusColor", "active", "depleted", "protected", "contested", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "_record$coordinates", "_record$coordinates2", "disabled", "value", "style", "fontSize", "faction", "_", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "level", "gutter", "marginBottom", "span", "size", "textAlign", "marginTop", "fontWeight", "length", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "max", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ResourceDistribution.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Rate,\n  Progress\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EnvironmentOutlined,\n  GoldOutlined,\n  ThunderboltOutlined,\n  FireOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ResourceDistribution = () => {\n  const { id: projectId } = useParams();\n  const [resources, setResources] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingResource, setEditingResource] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockResources = [\n    {\n      id: 1,\n      name: '灵石矿脉',\n      type: 'mineral',\n      location: '青云山脉',\n      coordinates: { x: 120, y: 80 },\n      rarity: 'rare',\n      difficulty: 4,\n      economicValue: 5,\n      description: '蕴含丰富灵气的矿脉，是修炼者必需的资源',\n      extractionMethod: '需要筑基期以上修为才能安全开采',\n      renewalRate: 'slow',\n      controllingFaction: '青云宗',\n      status: 'active'\n    },\n    {\n      id: 2,\n      name: '千年雪莲',\n      type: 'herb',\n      location: '雪域高原',\n      coordinates: { x: 200, y: 150 },\n      rarity: 'legendary',\n      difficulty: 5,\n      economicValue: 5,\n      description: '极其珍贵的炼丹材料，可延年益寿',\n      extractionMethod: '需要在特定时间采摘，且需要抵御严寒',\n      renewalRate: 'very_slow',\n      controllingFaction: '无',\n      status: 'protected'\n    }\n  ];\n\n  useEffect(() => {\n    loadResources();\n  }, [projectId]);\n\n  const loadResources = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setResources(mockResources);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载资源分布失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingResource(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (resource) => {\n    setEditingResource(resource);\n    form.setFieldsValue(resource);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 模拟API调用\n      setResources(resources.filter(r => r.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      if (editingResource) {\n        // 更新\n        setResources(resources.map(r =>\n          r.id === editingResource.id ? { ...r, ...values } : r\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newResource = {\n          id: Date.now(),\n          ...values\n        };\n        setResources([...resources, newResource]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      mineral: 'gold',\n      herb: 'green',\n      water: 'blue',\n      energy: 'purple',\n      artifact: 'red'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getRarityColor = (rarity) => {\n    const colors = {\n      common: 'default',\n      uncommon: 'blue',\n      rare: 'purple',\n      epic: 'orange',\n      legendary: 'red'\n    };\n    return colors[rarity] || 'default';\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      active: 'green',\n      depleted: 'red',\n      protected: 'blue',\n      contested: 'orange'\n    };\n    return colors[status] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '资源名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'mineral' ? '矿物' :\n             record.type === 'herb' ? '草药' :\n             record.type === 'water' ? '水源' :\n             record.type === 'energy' ? '能量' : '神器'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n      render: (text, record) => (\n        <Space>\n          <EnvironmentOutlined />\n          <Text>{text}</Text>\n          <Text type=\"secondary\">\n            ({record.coordinates?.x}, {record.coordinates?.y})\n          </Text>\n        </Space>\n      )\n    },\n    {\n      title: '稀有度',\n      dataIndex: 'rarity',\n      key: 'rarity',\n      render: (rarity) => (\n        <Tag color={getRarityColor(rarity)}>\n          {rarity === 'common' ? '普通' :\n           rarity === 'uncommon' ? '不常见' :\n           rarity === 'rare' ? '稀有' :\n           rarity === 'epic' ? '史诗' : '传说'}\n        </Tag>\n      )\n    },\n    {\n      title: '开采难度',\n      dataIndex: 'difficulty',\n      key: 'difficulty',\n      render: (difficulty) => (\n        <Rate disabled value={difficulty} style={{ fontSize: 16 }} />\n      )\n    },\n    {\n      title: '经济价值',\n      dataIndex: 'economicValue',\n      key: 'economicValue',\n      render: (value) => (\n        <Space>\n          <GoldOutlined style={{ color: '#faad14' }} />\n          <Rate disabled value={value} style={{ fontSize: 16 }} />\n        </Space>\n      )\n    },\n    {\n      title: '控制势力',\n      dataIndex: 'controllingFaction',\n      key: 'controllingFaction',\n      render: (faction) => faction || <Text type=\"secondary\">无</Text>\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'active' ? '活跃' :\n           status === 'depleted' ? '枯竭' :\n           status === 'protected' ? '受保护' : '争夺中'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个资源吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <EnvironmentOutlined /> 资源分布管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加资源\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <GoldOutlined style={{ fontSize: 24, color: '#faad14' }} />\n              <div style={{ marginTop: 8 }}>\n                <Text type=\"secondary\">总资源数</Text>\n                <div style={{ fontSize: 20, fontWeight: 'bold' }}>\n                  {resources.length}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <ThunderboltOutlined style={{ fontSize: 24, color: '#52c41a' }} />\n              <div style={{ marginTop: 8 }}>\n                <Text type=\"secondary\">活跃资源</Text>\n                <div style={{ fontSize: 20, fontWeight: 'bold' }}>\n                  {resources.filter(r => r.status === 'active').length}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <FireOutlined style={{ fontSize: 24, color: '#f5222d' }} />\n              <div style={{ marginTop: 8 }}>\n                <Text type=\"secondary\">争夺资源</Text>\n                <div style={{ fontSize: 20, fontWeight: 'bold' }}>\n                  {resources.filter(r => r.status === 'contested').length}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <div style={{ textAlign: 'center' }}>\n              <EnvironmentOutlined style={{ fontSize: 24, color: '#1890ff' }} />\n              <div style={{ marginTop: 8 }}>\n                <Text type=\"secondary\">受保护资源</Text>\n                <div style={{ fontSize: 20, fontWeight: 'bold' }}>\n                  {resources.filter(r => r.status === 'protected').length}\n                </div>\n              </div>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={resources}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个资源`\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingResource ? '编辑资源' : '添加资源'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'mineral',\n            rarity: 'common',\n            difficulty: 1,\n            economicValue: 1,\n            renewalRate: 'medium',\n            status: 'active'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"资源名称\"\n                rules={[{ required: true, message: '请输入资源名称' }]}\n              >\n                <Input placeholder=\"请输入资源名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"资源类型\"\n                rules={[{ required: true, message: '请选择资源类型' }]}\n              >\n                <Select>\n                  <Option value=\"mineral\">矿物</Option>\n                  <Option value=\"herb\">草药</Option>\n                  <Option value=\"water\">水源</Option>\n                  <Option value=\"energy\">能量</Option>\n                  <Option value=\"artifact\">神器</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"location\"\n                label=\"位置\"\n                rules={[{ required: true, message: '请输入位置' }]}\n              >\n                <Input placeholder=\"请输入位置\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"controllingFaction\"\n                label=\"控制势力\"\n              >\n                <Input placeholder=\"请输入控制势力\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"rarity\"\n                label=\"稀有度\"\n                rules={[{ required: true, message: '请选择稀有度' }]}\n              >\n                <Select>\n                  <Option value=\"common\">普通</Option>\n                  <Option value=\"uncommon\">不常见</Option>\n                  <Option value=\"rare\">稀有</Option>\n                  <Option value=\"epic\">史诗</Option>\n                  <Option value=\"legendary\">传说</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"difficulty\"\n                label=\"开采难度\"\n                rules={[{ required: true, message: '请选择开采难度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"economicValue\"\n                label=\"经济价值\"\n                rules={[{ required: true, message: '请选择经济价值' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"renewalRate\"\n                label=\"再生速度\"\n                rules={[{ required: true, message: '请选择再生速度' }]}\n              >\n                <Select>\n                  <Option value=\"very_slow\">极慢</Option>\n                  <Option value=\"slow\">缓慢</Option>\n                  <Option value=\"medium\">中等</Option>\n                  <Option value=\"fast\">快速</Option>\n                  <Option value=\"very_fast\">极快</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select>\n                  <Option value=\"active\">活跃</Option>\n                  <Option value=\"depleted\">枯竭</Option>\n                  <Option value=\"protected\">受保护</Option>\n                  <Option value=\"contested\">争夺中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请描述资源的特点和用途\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"extractionMethod\"\n            label=\"开采方法\"\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"请描述开采方法和注意事项\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ResourceDistribution;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,IAAI,EACJC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGnB,UAAU;AAClC,MAAM;EAAEoB;AAAS,CAAC,GAAGxB,KAAK;AAC1B,MAAM;EAAEyB;AAAO,CAAC,GAAGxB,MAAM;AAEzB,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGnC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4C,eAAe,EAAEC,kBAAkB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8C,IAAI,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG,CACpB;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,SAAS;IACfC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC9BC,MAAM,EAAE,MAAM;IACdC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,qBAAqB;IAClCC,gBAAgB,EAAE,iBAAiB;IACnCC,WAAW,EAAE,MAAM;IACnBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAE;EACV,CAAC,EACD;IACE1B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/BC,MAAM,EAAE,WAAW;IACnBC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,iBAAiB;IAC9BC,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,WAAW;IACxBC,kBAAkB,EAAE,GAAG;IACvBC,MAAM,EAAE;EACV,CAAC,CACF;EAED7D,SAAS,CAAC,MAAM;IACd8D,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC1B,SAAS,CAAC,CAAC;EAEf,MAAM0B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAuB,UAAU,CAAC,MAAM;QACfzB,YAAY,CAACS,aAAa,CAAC;QAC3BP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,UAAU,CAAC;MACzBxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IACtBrB,kBAAkB,CAAC,IAAI,CAAC;IACxBC,IAAI,CAACqB,WAAW,CAAC,CAAC;IAClBxB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyB,UAAU,GAAIC,QAAQ,IAAK;IAC/BxB,kBAAkB,CAACwB,QAAQ,CAAC;IAC5BvB,IAAI,CAACwB,cAAc,CAACD,QAAQ,CAAC;IAC7B1B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,YAAY,GAAG,MAAOnC,EAAE,IAAK;IACjC,IAAI;MACF;MACAG,YAAY,CAACD,SAAS,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrC,EAAE,KAAKA,EAAE,CAAC,CAAC;MAChDnB,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,IAAIhC,eAAe,EAAE;QACnB;QACAL,YAAY,CAACD,SAAS,CAACuC,GAAG,CAACJ,CAAC,IAC1BA,CAAC,CAACrC,EAAE,KAAKQ,eAAe,CAACR,EAAE,GAAG;UAAE,GAAGqC,CAAC;UAAE,GAAGG;QAAO,CAAC,GAAGH,CACtD,CAAC,CAAC;QACFxD,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMI,WAAW,GAAG;UAClB1C,EAAE,EAAE2C,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGJ;QACL,CAAC;QACDrC,YAAY,CAAC,CAAC,GAAGD,SAAS,EAAEwC,WAAW,CAAC,CAAC;QACzC7D,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;MACzB;MACA/B,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdhD,OAAO,CAACgD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAI/B,IAAI,IAAK;IAC7B,MAAMgC,MAAM,GAAG;MACbC,OAAO,EAAE,MAAM;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOL,MAAM,CAAChC,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAMsC,cAAc,GAAIjC,MAAM,IAAK;IACjC,MAAM2B,MAAM,GAAG;MACbO,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,MAAM;MAChBC,IAAI,EAAE,QAAQ;MACdC,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE;IACb,CAAC;IACD,OAAOX,MAAM,CAAC3B,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMuC,cAAc,GAAIhC,MAAM,IAAK;IACjC,MAAMoB,MAAM,GAAG;MACba,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOhB,MAAM,CAACpB,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMqC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB5E,OAAA,CAAClB,KAAK;MAAA+F,QAAA,gBACJ7E,OAAA,CAACE,IAAI;QAAC4E,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BlF,OAAA,CAACd,GAAG;QAACiG,KAAK,EAAE/B,YAAY,CAACwB,MAAM,CAACvD,IAAI,CAAE;QAAAwD,QAAA,EACnCD,MAAM,CAACvD,IAAI,KAAK,SAAS,GAAG,IAAI,GAChCuD,MAAM,CAACvD,IAAI,KAAK,MAAM,GAAG,IAAI,GAC7BuD,MAAM,CAACvD,IAAI,KAAK,OAAO,GAAG,IAAI,GAC9BuD,MAAM,CAACvD,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;MAAI;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM;MAAA,IAAAQ,mBAAA,EAAAC,oBAAA;MAAA,oBACnBrF,OAAA,CAAClB,KAAK;QAAA+F,QAAA,gBACJ7E,OAAA,CAACL,mBAAmB;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvBlF,OAAA,CAACE,IAAI;UAAA2E,QAAA,EAAEF;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACnBlF,OAAA,CAACE,IAAI;UAACmB,IAAI,EAAC,WAAW;UAAAwD,QAAA,GAAC,GACpB,GAAAO,mBAAA,GAACR,MAAM,CAACrD,WAAW,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoB5D,CAAC,EAAC,IAAE,GAAA6D,oBAAA,GAACT,MAAM,CAACrD,WAAW,cAAA8D,oBAAA,uBAAlBA,oBAAA,CAAoB5D,CAAC,EAAC,GACnD;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;EAEZ,CAAC,EACD;IACEX,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGhD,MAAM,iBACb1B,OAAA,CAACd,GAAG;MAACiG,KAAK,EAAExB,cAAc,CAACjC,MAAM,CAAE;MAAAmD,QAAA,EAChCnD,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,UAAU,GAAG,KAAK,GAC7BA,MAAM,KAAK,MAAM,GAAG,IAAI,GACxBA,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG;IAAI;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B;EAET,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAG/C,UAAU,iBACjB3B,OAAA,CAACV,IAAI;MAACgG,QAAQ;MAACC,KAAK,EAAE5D,UAAW;MAAC6D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAEhE,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAGa,KAAK,iBACZvF,OAAA,CAAClB,KAAK;MAAA+F,QAAA,gBACJ7E,OAAA,CAACJ,YAAY;QAAC4F,KAAK,EAAE;UAAEL,KAAK,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7ClF,OAAA,CAACV,IAAI;QAACgG,QAAQ;QAACC,KAAK,EAAEA,KAAM;QAACC,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,oBAAoB;IAC/BC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAGgB,OAAO,IAAKA,OAAO,iBAAI1F,OAAA,CAACE,IAAI;MAACmB,IAAI,EAAC,WAAW;MAAAwD,QAAA,EAAC;IAAC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAChE,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGzC,MAAM,iBACbjC,OAAA,CAACd,GAAG;MAACiG,KAAK,EAAElB,cAAc,CAAChC,MAAM,CAAE;MAAA4C,QAAA,EAChC5C,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,UAAU,GAAG,IAAI,GAC5BA,MAAM,KAAK,WAAW,GAAG,KAAK,GAAG;IAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACiB,CAAC,EAAEf,MAAM,kBAChB5E,OAAA,CAAClB,KAAK;MAAA+F,QAAA,gBACJ7E,OAAA,CAACX,OAAO;QAACkF,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB7E,OAAA,CAACxB,MAAM;UACL6C,IAAI,EAAC,MAAM;UACXuE,IAAI,eAAE5F,OAAA,CAACP,YAAY;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBW,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACqC,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlF,OAAA,CAACb,UAAU;QACToF,KAAK,EAAC,8DAAY;QAClBuB,SAAS,EAAEA,CAAA,KAAMpD,YAAY,CAACkC,MAAM,CAACrE,EAAE,CAAE;QACzCwF,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAnB,QAAA,eAEf7E,OAAA,CAACX,OAAO;UAACkF,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjB7E,OAAA,CAACxB,MAAM;YACL6C,IAAI,EAAC,MAAM;YACX4E,MAAM;YACNL,IAAI,eAAE5F,OAAA,CAACN,cAAc;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACElF,OAAA;IAAKkG,SAAS,EAAC,SAAS;IAAArB,QAAA,gBACtB7E,OAAA;MAAKkG,SAAS,EAAC,aAAa;MAAArB,QAAA,gBAC1B7E,OAAA,CAACC,KAAK;QAACkG,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAArB,QAAA,gBACrC7E,OAAA,CAACL,mBAAmB;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCACzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRlF,OAAA,CAACxB,MAAM;QACL6C,IAAI,EAAC,SAAS;QACduE,IAAI,eAAE5F,OAAA,CAACR,YAAY;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBW,OAAO,EAAExD,SAAU;QAAAwC,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlF,OAAA,CAAChB,GAAG;MAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACZ,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAG,CAAE;MAAAxB,QAAA,gBACjD7E,OAAA,CAACf,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAzB,QAAA,eACX7E,OAAA,CAAC1B,IAAI;UAACiI,IAAI,EAAC,OAAO;UAAA1B,QAAA,eAChB7E,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,gBAClC7E,OAAA,CAACJ,YAAY;cAAC4F,KAAK,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE;cAAU;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DlF,OAAA;cAAKwF,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAA5B,QAAA,gBAC3B7E,OAAA,CAACE,IAAI;gBAACmB,IAAI,EAAC,WAAW;gBAAAwD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClClF,OAAA;gBAAKwF,KAAK,EAAE;kBAAEC,QAAQ,EAAE,EAAE;kBAAEiB,UAAU,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,EAC9CpE,SAAS,CAACkG;cAAM;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACf,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAzB,QAAA,eACX7E,OAAA,CAAC1B,IAAI;UAACiI,IAAI,EAAC,OAAO;UAAA1B,QAAA,eAChB7E,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,gBAClC7E,OAAA,CAACH,mBAAmB;cAAC2F,KAAK,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE;cAAU;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClElF,OAAA;cAAKwF,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAA5B,QAAA,gBAC3B7E,OAAA,CAACE,IAAI;gBAACmB,IAAI,EAAC,WAAW;gBAAAwD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClClF,OAAA;gBAAKwF,KAAK,EAAE;kBAAEC,QAAQ,EAAE,EAAE;kBAAEiB,UAAU,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,EAC9CpE,SAAS,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACX,MAAM,KAAK,QAAQ,CAAC,CAAC0E;cAAM;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACf,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAzB,QAAA,eACX7E,OAAA,CAAC1B,IAAI;UAACiI,IAAI,EAAC,OAAO;UAAA1B,QAAA,eAChB7E,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,gBAClC7E,OAAA,CAACF,YAAY;cAAC0F,KAAK,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE;cAAU;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DlF,OAAA;cAAKwF,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAA5B,QAAA,gBAC3B7E,OAAA,CAACE,IAAI;gBAACmB,IAAI,EAAC,WAAW;gBAAAwD,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClClF,OAAA;gBAAKwF,KAAK,EAAE;kBAAEC,QAAQ,EAAE,EAAE;kBAAEiB,UAAU,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,EAC9CpE,SAAS,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACX,MAAM,KAAK,WAAW,CAAC,CAAC0E;cAAM;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlF,OAAA,CAACf,GAAG;QAACqH,IAAI,EAAE,CAAE;QAAAzB,QAAA,eACX7E,OAAA,CAAC1B,IAAI;UAACiI,IAAI,EAAC,OAAO;UAAA1B,QAAA,eAChB7E,OAAA;YAAKwF,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAA3B,QAAA,gBAClC7E,OAAA,CAACL,mBAAmB;cAAC6F,KAAK,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE;cAAU;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClElF,OAAA;cAAKwF,KAAK,EAAE;gBAAEiB,SAAS,EAAE;cAAE,CAAE;cAAA5B,QAAA,gBAC3B7E,OAAA,CAACE,IAAI;gBAACmB,IAAI,EAAC,WAAW;gBAAAwD,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnClF,OAAA;gBAAKwF,KAAK,EAAE;kBAAEC,QAAQ,EAAE,EAAE;kBAAEiB,UAAU,EAAE;gBAAO,CAAE;gBAAA7B,QAAA,EAC9CpE,SAAS,CAACkC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACX,MAAM,KAAK,WAAW,CAAC,CAAC0E;cAAM;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlF,OAAA,CAAC1B,IAAI;MAAAuG,QAAA,eACH7E,OAAA,CAACzB,KAAK;QACJ+F,OAAO,EAAEA,OAAQ;QACjBsC,UAAU,EAAEnG,SAAU;QACtBoG,MAAM,EAAC,IAAI;QACXlG,OAAO,EAAEA,OAAQ;QACjBmG,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPlF,OAAA,CAACvB,KAAK;MACJ8F,KAAK,EAAExD,eAAe,GAAG,MAAM,GAAG,MAAO;MACzCqG,IAAI,EAAEvG,YAAa;MACnBwG,QAAQ,EAAEA,CAAA,KAAMvG,eAAe,CAAC,KAAK,CAAE;MACvCwG,IAAI,EAAEA,CAAA,KAAMrG,IAAI,CAACsG,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAE7G,OAAQ;MACxB8G,KAAK,EAAE,GAAI;MAAA5C,QAAA,eAEX7E,OAAA,CAACtB,IAAI;QACHuC,IAAI,EAAEA,IAAK;QACXyG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7E,YAAa;QACvB8E,aAAa,EAAE;UACbvG,IAAI,EAAE,SAAS;UACfK,MAAM,EAAE,QAAQ;UAChBC,UAAU,EAAE,CAAC;UACbC,aAAa,EAAE,CAAC;UAChBG,WAAW,EAAE,QAAQ;UACrBE,MAAM,EAAE;QACV,CAAE;QAAA4C,QAAA,gBAEF7E,OAAA,CAAChB,GAAG;UAACoH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACd7E,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,MAAM;cACX0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyF,QAAA,eAEhD7E,OAAA,CAACrB,KAAK;gBAACsJ,WAAW,EAAC;cAAS;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,MAAM;cACX0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyF,QAAA,eAEhD7E,OAAA,CAACpB,MAAM;gBAAAiG,QAAA,gBACL7E,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,UAAU;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAAChB,GAAG;UAACoH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACd7E,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,UAAU;cACf0G,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyF,QAAA,eAE9C7E,OAAA,CAACrB,KAAK;gBAACsJ,WAAW,EAAC;cAAO;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,oBAAoB;cACzB0G,KAAK,EAAC,0BAAM;cAAAjD,QAAA,eAEZ7E,OAAA,CAACrB,KAAK;gBAACsJ,WAAW,EAAC;cAAS;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAAChB,GAAG;UAACoH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACd7E,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAzB,QAAA,eACX7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,QAAQ;cACb0G,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAyF,QAAA,eAE/C7E,OAAA,CAACpB,MAAM;gBAAAiG,QAAA,gBACL7E,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,UAAU;kBAAAV,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAzB,QAAA,eACX7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,YAAY;cACjB0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyF,QAAA,eAEhD7E,OAAA,CAACnB,WAAW;gBAACqJ,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAC3C,KAAK,EAAE;kBAAEiC,KAAK,EAAE;gBAAO;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,CAAE;YAAAzB,QAAA,eACX7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,eAAe;cACpB0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyF,QAAA,eAEhD7E,OAAA,CAACnB,WAAW;gBAACqJ,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAC3C,KAAK,EAAE;kBAAEiC,KAAK,EAAE;gBAAO;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAAChB,GAAG;UAACoH,MAAM,EAAE,EAAG;UAAAvB,QAAA,gBACd7E,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,aAAa;cAClB0G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyF,QAAA,eAEhD7E,OAAA,CAACpB,MAAM;gBAAAiG,QAAA,gBACL7E,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlF,OAAA,CAACf,GAAG;YAACqH,IAAI,EAAE,EAAG;YAAAzB,QAAA,eACZ7E,OAAA,CAACtB,IAAI,CAACmJ,IAAI;cACRzG,IAAI,EAAC,QAAQ;cACb0G,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5I,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyF,QAAA,eAE9C7E,OAAA,CAACpB,MAAM;gBAAAiG,QAAA,gBACL7E,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,QAAQ;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,UAAU;kBAAAV,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClF,OAAA,CAACI,MAAM;kBAACmF,KAAK,EAAC,WAAW;kBAAAV,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlF,OAAA,CAACtB,IAAI,CAACmJ,IAAI;UACRzG,IAAI,EAAC,aAAa;UAClB0G,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE5I,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAyF,QAAA,eAE9C7E,OAAA,CAACG,QAAQ;YACPiI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAa;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlF,OAAA,CAACtB,IAAI,CAACmJ,IAAI;UACRzG,IAAI,EAAC,kBAAkB;UACvB0G,KAAK,EAAC,0BAAM;UAAAjD,QAAA,eAEZ7E,OAAA,CAACG,QAAQ;YACPiI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAc;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CA9eID,oBAAoB;EAAA,QACEhC,SAAS,EAKpBK,IAAI,CAACwC,OAAO;AAAA;AAAAmH,EAAA,GANvBhI,oBAAoB;AAgf1B,eAAeA,oBAAoB;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}