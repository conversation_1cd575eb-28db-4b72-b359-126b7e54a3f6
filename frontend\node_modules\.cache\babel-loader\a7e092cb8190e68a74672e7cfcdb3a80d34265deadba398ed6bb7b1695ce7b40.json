{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\DimensionStructure.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Statistic, Rate } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, GatewayOutlined, StarOutlined, ThunderboltOutlined, EyeOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst DimensionStructure = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [dimensions, setDimensions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingDimension, setEditingDimension] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockDimensions = [{\n    id: 1,\n    name: '主物质界',\n    type: 'material',\n    level: 1,\n    stability: 5,\n    timeFlow: 1.0,\n    spaceSize: 'infinite',\n    lawStrength: 5,\n    energyDensity: 3,\n    accessMethods: ['自然存在'],\n    restrictions: ['无'],\n    inhabitants: ['人族', '妖族', '魔族'],\n    uniqueFeatures: ['完整的天地法则', '稳定的时空结构'],\n    description: '修仙世界的主要维度，所有生灵的家园',\n    connectedDimensions: ['灵界', '魔界'],\n    dangerLevel: 2,\n    discoveredBy: '天然存在',\n    status: 'stable'\n  }, {\n    id: 2,\n    name: '灵界',\n    type: 'spiritual',\n    level: 2,\n    stability: 4,\n    timeFlow: 0.5,\n    spaceSize: 'vast',\n    lawStrength: 4,\n    energyDensity: 5,\n    accessMethods: ['渡劫飞升', '空间裂缝'],\n    restrictions: ['需要元婴期以上修为'],\n    inhabitants: ['仙人', '灵兽', '天使'],\n    uniqueFeatures: ['灵气极度浓郁', '时间流速缓慢'],\n    description: '修仙者向往的高等维度，灵气充沛',\n    connectedDimensions: ['主物质界', '仙界'],\n    dangerLevel: 3,\n    discoveredBy: '古代仙人',\n    status: 'stable'\n  }, {\n    id: 3,\n    name: '虚空乱流',\n    type: 'chaotic',\n    level: 0,\n    stability: 1,\n    timeFlow: 'variable',\n    spaceSize: 'unknown',\n    lawStrength: 1,\n    energyDensity: 2,\n    accessMethods: ['空间撕裂', '意外传送'],\n    restrictions: ['极度危险'],\n    inhabitants: ['虚空生物', '迷失者'],\n    uniqueFeatures: ['时空混乱', '法则不稳定'],\n    description: '危险的混沌维度，充满未知的威胁',\n    connectedDimensions: ['所有维度'],\n    dangerLevel: 5,\n    discoveredBy: '意外发现',\n    status: 'chaotic'\n  }];\n  useEffect(() => {\n    loadDimensions();\n  }, [projectId]);\n  const loadDimensions = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setDimensions(mockDimensions);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载维度结构失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingDimension(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = dimension => {\n    var _dimension$accessMeth, _dimension$restrictio, _dimension$inhabitant, _dimension$uniqueFeat, _dimension$connectedD;\n    setEditingDimension(dimension);\n    form.setFieldsValue({\n      ...dimension,\n      accessMethods: (_dimension$accessMeth = dimension.accessMethods) === null || _dimension$accessMeth === void 0 ? void 0 : _dimension$accessMeth.join(', '),\n      restrictions: (_dimension$restrictio = dimension.restrictions) === null || _dimension$restrictio === void 0 ? void 0 : _dimension$restrictio.join(', '),\n      inhabitants: (_dimension$inhabitant = dimension.inhabitants) === null || _dimension$inhabitant === void 0 ? void 0 : _dimension$inhabitant.join(', '),\n      uniqueFeatures: (_dimension$uniqueFeat = dimension.uniqueFeatures) === null || _dimension$uniqueFeat === void 0 ? void 0 : _dimension$uniqueFeat.join(', '),\n      connectedDimensions: (_dimension$connectedD = dimension.connectedDimensions) === null || _dimension$connectedD === void 0 ? void 0 : _dimension$connectedD.join(', ')\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 模拟API调用\n      setDimensions(dimensions.filter(d => d.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$accessMethods, _values$restrictions, _values$inhabitants, _values$uniqueFeature, _values$connectedDime;\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        level: values.level,\n        stability: values.stability,\n        timeFlow: values.timeFlow,\n        spaceSize: values.spaceSize,\n        lawStrength: values.lawStrength,\n        energyDensity: values.energyDensity,\n        accessMethods: ((_values$accessMethods = values.accessMethods) === null || _values$accessMethods === void 0 ? void 0 : _values$accessMethods.split(',').map(m => m.trim()).filter(m => m)) || [],\n        restrictions: ((_values$restrictions = values.restrictions) === null || _values$restrictions === void 0 ? void 0 : _values$restrictions.split(',').map(r => r.trim()).filter(r => r)) || [],\n        inhabitants: ((_values$inhabitants = values.inhabitants) === null || _values$inhabitants === void 0 ? void 0 : _values$inhabitants.split(',').map(i => i.trim()).filter(i => i)) || [],\n        uniqueFeatures: ((_values$uniqueFeature = values.uniqueFeatures) === null || _values$uniqueFeature === void 0 ? void 0 : _values$uniqueFeature.split(',').map(f => f.trim()).filter(f => f)) || [],\n        description: values.description,\n        connectedDimensions: ((_values$connectedDime = values.connectedDimensions) === null || _values$connectedDime === void 0 ? void 0 : _values$connectedDime.split(',').map(d => d.trim()).filter(d => d)) || [],\n        dangerLevel: values.dangerLevel,\n        discoveredBy: values.discoveredBy,\n        status: values.status\n      };\n      if (editingDimension) {\n        // 更新\n        setDimensions(dimensions.map(d => d.id === editingDimension.id ? {\n          ...d,\n          ...processedValues\n        } : d));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newDimension = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setDimensions([...dimensions, newDimension]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      material: 'blue',\n      spiritual: 'purple',\n      elemental: 'orange',\n      chaotic: 'red',\n      divine: 'gold',\n      shadow: 'black'\n    };\n    return colors[type] || 'default';\n  };\n  const getStatusColor = status => {\n    const colors = {\n      stable: 'green',\n      unstable: 'orange',\n      chaotic: 'red',\n      sealed: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n  const columns = [{\n    title: '维度名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'material' ? '物质' : record.type === 'spiritual' ? '灵界' : record.type === 'elemental' ? '元素' : record.type === 'chaotic' ? '混沌' : record.type === 'divine' ? '神界' : '阴影'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '维度等级',\n    dataIndex: 'level',\n    key: 'level',\n    render: level => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(StarOutlined, {\n        style: {\n          color: '#faad14'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: level\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.level - b.level\n  }, {\n    title: '稳定性',\n    dataIndex: 'stability',\n    key: 'stability',\n    render: stability => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: stability,\n      style: {\n        fontSize: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.stability - b.stability\n  }, {\n    title: '时间流速',\n    dataIndex: 'timeFlow',\n    key: 'timeFlow',\n    render: flow => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: typeof flow === 'number' ? `${flow}x` : flow\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '法则强度',\n    dataIndex: 'lawStrength',\n    key: 'lawStrength',\n    render: strength => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {\n        style: {\n          color: '#722ed1'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Rate, {\n        disabled: true,\n        value: strength,\n        style: {\n          fontSize: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.lawStrength - b.lawStrength\n  }, {\n    title: '危险等级',\n    dataIndex: 'dangerLevel',\n    key: 'dangerLevel',\n    render: level => {\n      const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: [level, \"/5\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 16\n      }, this);\n    },\n    sorter: (a, b) => a.dangerLevel - b.dangerLevel\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status === 'stable' ? '稳定' : status === 'unstable' ? '不稳定' : status === 'chaotic' ? '混沌' : '封印'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u7EF4\\u5EA6\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(GatewayOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), \" \\u7EF4\\u5EA6\\u7ED3\\u6784\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u7EF4\\u5EA6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7EF4\\u5EA6\\u603B\\u6570\",\n            value: dimensions.length,\n            prefix: /*#__PURE__*/_jsxDEV(GatewayOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7A33\\u5B9A\\u7EF4\\u5EA6\",\n            value: dimensions.filter(d => d.status === 'stable').length,\n            prefix: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6DF7\\u6C8C\\u7EF4\\u5EA6\",\n            value: dimensions.filter(d => d.status === 'chaotic').length,\n            prefix: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5371\\u9669\\u7B49\\u7EA7\",\n            value: dimensions.length > 0 ? (dimensions.reduce((sum, d) => sum + d.dangerLevel, 0) / dimensions.length).toFixed(1) : 0,\n            prefix: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: dimensions,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个维度`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDimension ? '编辑维度' : '添加维度',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 900,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'material',\n          level: 1,\n          stability: 3,\n          timeFlow: 1.0,\n          spaceSize: 'large',\n          lawStrength: 3,\n          energyDensity: 3,\n          dangerLevel: 1,\n          status: 'stable'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u7EF4\\u5EA6\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入维度名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EF4\\u5EA6\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u7EF4\\u5EA6\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择维度类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"material\",\n                  children: \"\\u7269\\u8D28\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"spiritual\",\n                  children: \"\\u7075\\u754C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"elemental\",\n                  children: \"\\u5143\\u7D20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"chaotic\",\n                  children: \"\\u6DF7\\u6C8C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"divine\",\n                  children: \"\\u795E\\u754C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"shadow\",\n                  children: \"\\u9634\\u5F71\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"\\u7EF4\\u5EA6\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请输入维度等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                max: 10,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stability\",\n              label: \"\\u7A33\\u5B9A\\u6027\",\n              rules: [{\n                required: true,\n                message: '请选择稳定性'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dangerLevel\",\n              label: \"\\u5371\\u9669\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择危险等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"timeFlow\",\n              label: \"\\u65F6\\u95F4\\u6D41\\u901F\",\n              rules: [{\n                required: true,\n                message: '请输入时间流速'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                step: 0.1,\n                style: {\n                  width: '100%'\n                },\n                addonAfter: \"x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"lawStrength\",\n              label: \"\\u6CD5\\u5219\\u5F3A\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择法则强度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"energyDensity\",\n              label: \"\\u80FD\\u91CF\\u5BC6\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择能量密度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"spaceSize\",\n              label: \"\\u7A7A\\u95F4\\u5927\\u5C0F\",\n              rules: [{\n                required: true,\n                message: '请选择空间大小'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"tiny\",\n                  children: \"\\u5FAE\\u5C0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"small\",\n                  children: \"\\u5C0F\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"large\",\n                  children: \"\\u5927\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"vast\",\n                  children: \"\\u5E7F\\u9614\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"infinite\",\n                  children: \"\\u65E0\\u9650\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"unknown\",\n                  children: \"\\u672A\\u77E5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"stable\",\n                  children: \"\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"unstable\",\n                  children: \"\\u4E0D\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"chaotic\",\n                  children: \"\\u6DF7\\u6C8C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"sealed\",\n                  children: \"\\u5C01\\u5370\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"discoveredBy\",\n          label: \"\\u53D1\\u73B0\\u8005\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u53D1\\u73B0\\u8005\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"accessMethods\",\n          label: \"\\u8FDB\\u5165\\u65B9\\u6CD5\",\n          extra: \"\\u591A\\u4E2A\\u65B9\\u6CD5\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u6E21\\u52AB\\u98DE\\u5347, \\u7A7A\\u95F4\\u88C2\\u7F1D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"restrictions\",\n          label: \"\\u8FDB\\u5165\\u9650\\u5236\",\n          extra: \"\\u591A\\u4E2A\\u9650\\u5236\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u9700\\u8981\\u5143\\u5A74\\u671F\\u4EE5\\u4E0A\\u4FEE\\u4E3A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"inhabitants\",\n          label: \"\\u5C45\\u4F4F\\u8005\",\n          extra: \"\\u591A\\u4E2A\\u79CD\\u65CF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u4ED9\\u4EBA, \\u7075\\u517D, \\u5929\\u4F7F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"uniqueFeatures\",\n          label: \"\\u72EC\\u7279\\u7279\\u5F81\",\n          extra: \"\\u591A\\u4E2A\\u7279\\u5F81\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u7075\\u6C14\\u6781\\u5EA6\\u6D53\\u90C1, \\u65F6\\u95F4\\u6D41\\u901F\\u7F13\\u6162\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"connectedDimensions\",\n          label: \"\\u8FDE\\u63A5\\u7EF4\\u5EA6\",\n          extra: \"\\u591A\\u4E2A\\u7EF4\\u5EA6\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u4E3B\\u7269\\u8D28\\u754C, \\u4ED9\\u754C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u7EF4\\u5EA6\\u7684\\u7279\\u70B9\\u3001\\u73AF\\u5883\\u3001\\u5386\\u53F2\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 338,\n    columnNumber: 5\n  }, this);\n};\n_s(DimensionStructure, \"2YnPAO5CZehUJle6xfmxcVnOxCQ=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = DimensionStructure;\nexport default DimensionStructure;\nvar _c;\n$RefreshReg$(_c, \"DimensionStructure\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Statistic", "Rate", "PlusOutlined", "EditOutlined", "DeleteOutlined", "GatewayOutlined", "StarOutlined", "ThunderboltOutlined", "EyeOutlined", "ClockCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "DimensionStructure", "_s", "id", "projectId", "dimensions", "setDimensions", "loading", "setLoading", "modalVisible", "setModalVisible", "editingDimension", "setEditingDimension", "form", "useForm", "mockDimensions", "name", "type", "level", "stability", "timeFlow", "spaceSize", "lawStrength", "energyDensity", "accessMethods", "restrictions", "inhabitants", "uniqueFeatures", "description", "connectedDimensions", "dangerLevel", "discoveredBy", "status", "loadDimensions", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "dimension", "_dimension$accessMeth", "_dimension$restrictio", "_dimension$inhabitant", "_dimension$uniqueFeat", "_dimension$connectedD", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "handleDelete", "filter", "d", "success", "handleSubmit", "values", "_values$accessMethods", "_values$restrictions", "_values$inhabitants", "_values$uniqueFeature", "_values$connectedDime", "processedValues", "split", "map", "m", "trim", "r", "i", "f", "newDimension", "Date", "now", "getTypeColor", "colors", "material", "spiritual", "elemental", "chaotic", "divine", "shadow", "getStatusColor", "stable", "unstable", "sealed", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "style", "sorter", "a", "b", "disabled", "value", "fontSize", "flow", "strength", "_", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "gutter", "marginBottom", "span", "size", "length", "prefix", "valueStyle", "reduce", "sum", "toFixed", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "max", "step", "addonAfter", "extra", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/DimensionStructure.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Statistic,\n  Rate\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  GatewayOutlined,\n  StarOutlined,\n  ThunderboltOutlined,\n  EyeOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst DimensionStructure = () => {\n  const { id: projectId } = useParams();\n  const [dimensions, setDimensions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingDimension, setEditingDimension] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockDimensions = [\n    {\n      id: 1,\n      name: '主物质界',\n      type: 'material',\n      level: 1,\n      stability: 5,\n      timeFlow: 1.0,\n      spaceSize: 'infinite',\n      lawStrength: 5,\n      energyDensity: 3,\n      accessMethods: ['自然存在'],\n      restrictions: ['无'],\n      inhabitants: ['人族', '妖族', '魔族'],\n      uniqueFeatures: ['完整的天地法则', '稳定的时空结构'],\n      description: '修仙世界的主要维度，所有生灵的家园',\n      connectedDimensions: ['灵界', '魔界'],\n      dangerLevel: 2,\n      discoveredBy: '天然存在',\n      status: 'stable'\n    },\n    {\n      id: 2,\n      name: '灵界',\n      type: 'spiritual',\n      level: 2,\n      stability: 4,\n      timeFlow: 0.5,\n      spaceSize: 'vast',\n      lawStrength: 4,\n      energyDensity: 5,\n      accessMethods: ['渡劫飞升', '空间裂缝'],\n      restrictions: ['需要元婴期以上修为'],\n      inhabitants: ['仙人', '灵兽', '天使'],\n      uniqueFeatures: ['灵气极度浓郁', '时间流速缓慢'],\n      description: '修仙者向往的高等维度，灵气充沛',\n      connectedDimensions: ['主物质界', '仙界'],\n      dangerLevel: 3,\n      discoveredBy: '古代仙人',\n      status: 'stable'\n    },\n    {\n      id: 3,\n      name: '虚空乱流',\n      type: 'chaotic',\n      level: 0,\n      stability: 1,\n      timeFlow: 'variable',\n      spaceSize: 'unknown',\n      lawStrength: 1,\n      energyDensity: 2,\n      accessMethods: ['空间撕裂', '意外传送'],\n      restrictions: ['极度危险'],\n      inhabitants: ['虚空生物', '迷失者'],\n      uniqueFeatures: ['时空混乱', '法则不稳定'],\n      description: '危险的混沌维度，充满未知的威胁',\n      connectedDimensions: ['所有维度'],\n      dangerLevel: 5,\n      discoveredBy: '意外发现',\n      status: 'chaotic'\n    }\n  ];\n\n  useEffect(() => {\n    loadDimensions();\n  }, [projectId]);\n\n  const loadDimensions = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setDimensions(mockDimensions);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载维度结构失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingDimension(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (dimension) => {\n    setEditingDimension(dimension);\n    form.setFieldsValue({\n      ...dimension,\n      accessMethods: dimension.accessMethods?.join(', '),\n      restrictions: dimension.restrictions?.join(', '),\n      inhabitants: dimension.inhabitants?.join(', '),\n      uniqueFeatures: dimension.uniqueFeatures?.join(', '),\n      connectedDimensions: dimension.connectedDimensions?.join(', ')\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 模拟API调用\n      setDimensions(dimensions.filter(d => d.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        level: values.level,\n        stability: values.stability,\n        timeFlow: values.timeFlow,\n        spaceSize: values.spaceSize,\n        lawStrength: values.lawStrength,\n        energyDensity: values.energyDensity,\n        accessMethods: values.accessMethods?.split(',').map(m => m.trim()).filter(m => m) || [],\n        restrictions: values.restrictions?.split(',').map(r => r.trim()).filter(r => r) || [],\n        inhabitants: values.inhabitants?.split(',').map(i => i.trim()).filter(i => i) || [],\n        uniqueFeatures: values.uniqueFeatures?.split(',').map(f => f.trim()).filter(f => f) || [],\n        description: values.description,\n        connectedDimensions: values.connectedDimensions?.split(',').map(d => d.trim()).filter(d => d) || [],\n        dangerLevel: values.dangerLevel,\n        discoveredBy: values.discoveredBy,\n        status: values.status\n      };\n\n      if (editingDimension) {\n        // 更新\n        setDimensions(dimensions.map(d =>\n          d.id === editingDimension.id ? { ...d, ...processedValues } : d\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newDimension = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setDimensions([...dimensions, newDimension]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      material: 'blue',\n      spiritual: 'purple',\n      elemental: 'orange',\n      chaotic: 'red',\n      divine: 'gold',\n      shadow: 'black'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      stable: 'green',\n      unstable: 'orange',\n      chaotic: 'red',\n      sealed: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '维度名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'material' ? '物质' :\n             record.type === 'spiritual' ? '灵界' :\n             record.type === 'elemental' ? '元素' :\n             record.type === 'chaotic' ? '混沌' :\n             record.type === 'divine' ? '神界' : '阴影'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '维度等级',\n      dataIndex: 'level',\n      key: 'level',\n      render: (level) => (\n        <Space>\n          <StarOutlined style={{ color: '#faad14' }} />\n          <Text>{level}</Text>\n        </Space>\n      ),\n      sorter: (a, b) => a.level - b.level\n    },\n    {\n      title: '稳定性',\n      dataIndex: 'stability',\n      key: 'stability',\n      render: (stability) => (\n        <Rate disabled value={stability} style={{ fontSize: 16 }} />\n      ),\n      sorter: (a, b) => a.stability - b.stability\n    },\n    {\n      title: '时间流速',\n      dataIndex: 'timeFlow',\n      key: 'timeFlow',\n      render: (flow) => (\n        <Space>\n          <ClockCircleOutlined />\n          <Text>{typeof flow === 'number' ? `${flow}x` : flow}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '法则强度',\n      dataIndex: 'lawStrength',\n      key: 'lawStrength',\n      render: (strength) => (\n        <Space>\n          <ThunderboltOutlined style={{ color: '#722ed1' }} />\n          <Rate disabled value={strength} style={{ fontSize: 16 }} />\n        </Space>\n      ),\n      sorter: (a, b) => a.lawStrength - b.lawStrength\n    },\n    {\n      title: '危险等级',\n      dataIndex: 'dangerLevel',\n      key: 'dangerLevel',\n      render: (level) => {\n        const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n        return <Tag color={color}>{level}/5</Tag>;\n      },\n      sorter: (a, b) => a.dangerLevel - b.dangerLevel\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'stable' ? '稳定' :\n           status === 'unstable' ? '不稳定' :\n           status === 'chaotic' ? '混沌' : '封印'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个维度吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <GatewayOutlined /> 维度结构管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加维度\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"维度总数\"\n              value={dimensions.length}\n              prefix={<GatewayOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"稳定维度\"\n              value={dimensions.filter(d => d.status === 'stable').length}\n              prefix={<StarOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"混沌维度\"\n              value={dimensions.filter(d => d.status === 'chaotic').length}\n              prefix={<ThunderboltOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"平均危险等级\"\n              value={dimensions.length > 0 ? (dimensions.reduce((sum, d) => sum + d.dangerLevel, 0) / dimensions.length).toFixed(1) : 0}\n              prefix={<EyeOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={dimensions}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个维度`\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingDimension ? '编辑维度' : '添加维度'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={900}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'material',\n            level: 1,\n            stability: 3,\n            timeFlow: 1.0,\n            spaceSize: 'large',\n            lawStrength: 3,\n            energyDensity: 3,\n            dangerLevel: 1,\n            status: 'stable'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"维度名称\"\n                rules={[{ required: true, message: '请输入维度名称' }]}\n              >\n                <Input placeholder=\"请输入维度名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"维度类型\"\n                rules={[{ required: true, message: '请选择维度类型' }]}\n              >\n                <Select>\n                  <Option value=\"material\">物质</Option>\n                  <Option value=\"spiritual\">灵界</Option>\n                  <Option value=\"elemental\">元素</Option>\n                  <Option value=\"chaotic\">混沌</Option>\n                  <Option value=\"divine\">神界</Option>\n                  <Option value=\"shadow\">阴影</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"维度等级\"\n                rules={[{ required: true, message: '请输入维度等级' }]}\n              >\n                <InputNumber min={0} max={10} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"stability\"\n                label=\"稳定性\"\n                rules={[{ required: true, message: '请选择稳定性' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"dangerLevel\"\n                label=\"危险等级\"\n                rules={[{ required: true, message: '请选择危险等级' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"timeFlow\"\n                label=\"时间流速\"\n                rules={[{ required: true, message: '请输入时间流速' }]}\n              >\n                <InputNumber min={0} step={0.1} style={{ width: '100%' }} addonAfter=\"x\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"lawStrength\"\n                label=\"法则强度\"\n                rules={[{ required: true, message: '请选择法则强度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"energyDensity\"\n                label=\"能量密度\"\n                rules={[{ required: true, message: '请选择能量密度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"spaceSize\"\n                label=\"空间大小\"\n                rules={[{ required: true, message: '请选择空间大小' }]}\n              >\n                <Select>\n                  <Option value=\"tiny\">微小</Option>\n                  <Option value=\"small\">小型</Option>\n                  <Option value=\"medium\">中型</Option>\n                  <Option value=\"large\">大型</Option>\n                  <Option value=\"vast\">广阔</Option>\n                  <Option value=\"infinite\">无限</Option>\n                  <Option value=\"unknown\">未知</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select>\n                  <Option value=\"stable\">稳定</Option>\n                  <Option value=\"unstable\">不稳定</Option>\n                  <Option value=\"chaotic\">混沌</Option>\n                  <Option value=\"sealed\">封印</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"discoveredBy\" label=\"发现者\">\n            <Input placeholder=\"请输入发现者\" />\n          </Form.Item>\n\n          <Form.Item name=\"accessMethods\" label=\"进入方法\" extra=\"多个方法请用逗号分隔\">\n            <Input placeholder=\"如：渡劫飞升, 空间裂缝\" />\n          </Form.Item>\n\n          <Form.Item name=\"restrictions\" label=\"进入限制\" extra=\"多个限制请用逗号分隔\">\n            <Input placeholder=\"如：需要元婴期以上修为\" />\n          </Form.Item>\n\n          <Form.Item name=\"inhabitants\" label=\"居住者\" extra=\"多个种族请用逗号分隔\">\n            <Input placeholder=\"如：仙人, 灵兽, 天使\" />\n          </Form.Item>\n\n          <Form.Item name=\"uniqueFeatures\" label=\"独特特征\" extra=\"多个特征请用逗号分隔\">\n            <Input placeholder=\"如：灵气极度浓郁, 时间流速缓慢\" />\n          </Form.Item>\n\n          <Form.Item name=\"connectedDimensions\" label=\"连接维度\" extra=\"多个维度请用逗号分隔\">\n            <Input placeholder=\"如：主物质界, 仙界\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <TextArea rows={3} placeholder=\"请描述维度的特点、环境、历史等\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default DimensionStructure;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,SAAS,EACTC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,QACd,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAS,CAAC,GAAGzB,KAAK;AAC1B,MAAM;EAAE0B;AAAO,CAAC,GAAGzB,MAAM;AAEzB,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGpC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,cAAc,GAAG,CACrB;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,UAAU;IACrBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,CAAC;IACvBC,YAAY,EAAE,CAAC,GAAG,CAAC;IACnBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACtCC,WAAW,EAAE,mBAAmB;IAChCC,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACjCC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC/BC,YAAY,EAAE,CAAC,WAAW,CAAC;IAC3BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,cAAc,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpCC,WAAW,EAAE,iBAAiB;IAC9BC,mBAAmB,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACnCC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC/BC,YAAY,EAAE,CAAC,MAAM,CAAC;IACtBC,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IAC5BC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IACjCC,WAAW,EAAE,iBAAiB;IAC9BC,mBAAmB,EAAE,CAAC,MAAM,CAAC;IAC7BC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,CACF;EAEDjE,SAAS,CAAC,MAAM;IACdkE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EAEf,MAAM6B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA0B,UAAU,CAAC,MAAM;QACf5B,aAAa,CAACS,cAAc,CAAC;QAC7BP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;MACzB3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,SAAS,GAAGA,CAAA,KAAM;IACtBxB,mBAAmB,CAAC,IAAI,CAAC;IACzBC,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB3B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,UAAU,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAChChC,mBAAmB,CAAC2B,SAAS,CAAC;IAC9B1B,IAAI,CAACgC,cAAc,CAAC;MAClB,GAAGN,SAAS;MACZf,aAAa,GAAAgB,qBAAA,GAAED,SAAS,CAACf,aAAa,cAAAgB,qBAAA,uBAAvBA,qBAAA,CAAyBM,IAAI,CAAC,IAAI,CAAC;MAClDrB,YAAY,GAAAgB,qBAAA,GAAEF,SAAS,CAACd,YAAY,cAAAgB,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,IAAI,CAAC;MAChDpB,WAAW,GAAAgB,qBAAA,GAAEH,SAAS,CAACb,WAAW,cAAAgB,qBAAA,uBAArBA,qBAAA,CAAuBI,IAAI,CAAC,IAAI,CAAC;MAC9CnB,cAAc,GAAAgB,qBAAA,GAAEJ,SAAS,CAACZ,cAAc,cAAAgB,qBAAA,uBAAxBA,qBAAA,CAA0BG,IAAI,CAAC,IAAI,CAAC;MACpDjB,mBAAmB,GAAAe,qBAAA,GAAEL,SAAS,CAACV,mBAAmB,cAAAe,qBAAA,uBAA7BA,qBAAA,CAA+BE,IAAI,CAAC,IAAI;IAC/D,CAAC,CAAC;IACFpC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqC,YAAY,GAAG,MAAO5C,EAAE,IAAK;IACjC,IAAI;MACF;MACAG,aAAa,CAACD,UAAU,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC,CAAC;MAClDpB,OAAO,CAACmE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACF,MAAMC,eAAe,GAAG;QACtB1C,IAAI,EAAEoC,MAAM,CAACpC,IAAI;QACjBC,IAAI,EAAEmC,MAAM,CAACnC,IAAI;QACjBC,KAAK,EAAEkC,MAAM,CAAClC,KAAK;QACnBC,SAAS,EAAEiC,MAAM,CAACjC,SAAS;QAC3BC,QAAQ,EAAEgC,MAAM,CAAChC,QAAQ;QACzBC,SAAS,EAAE+B,MAAM,CAAC/B,SAAS;QAC3BC,WAAW,EAAE8B,MAAM,CAAC9B,WAAW;QAC/BC,aAAa,EAAE6B,MAAM,CAAC7B,aAAa;QACnCC,aAAa,EAAE,EAAA6B,qBAAA,GAAAD,MAAM,CAAC5B,aAAa,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACa,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACvFpC,YAAY,EAAE,EAAA6B,oBAAA,GAAAF,MAAM,CAAC3B,YAAY,cAAA6B,oBAAA,uBAAnBA,oBAAA,CAAqBK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACe,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACrFrC,WAAW,EAAE,EAAA6B,mBAAA,GAAAH,MAAM,CAAC1B,WAAW,cAAA6B,mBAAA,uBAAlBA,mBAAA,CAAoBI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACI,CAAC,IAAIA,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACnFrC,cAAc,EAAE,EAAA6B,qBAAA,GAAAJ,MAAM,CAACzB,cAAc,cAAA6B,qBAAA,uBAArBA,qBAAA,CAAuBG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACzFrC,WAAW,EAAEwB,MAAM,CAACxB,WAAW;QAC/BC,mBAAmB,EAAE,EAAA4B,qBAAA,GAAAL,MAAM,CAACvB,mBAAmB,cAAA4B,qBAAA,uBAA1BA,qBAAA,CAA4BE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACa,IAAI,CAAC,CAAC,CAAC,CAACd,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACnGnB,WAAW,EAAEsB,MAAM,CAACtB,WAAW;QAC/BC,YAAY,EAAEqB,MAAM,CAACrB,YAAY;QACjCC,MAAM,EAAEoB,MAAM,CAACpB;MACjB,CAAC;MAED,IAAIrB,gBAAgB,EAAE;QACpB;QACAL,aAAa,CAACD,UAAU,CAACuD,GAAG,CAACX,CAAC,IAC5BA,CAAC,CAAC9C,EAAE,KAAKQ,gBAAgB,CAACR,EAAE,GAAG;UAAE,GAAG8C,CAAC;UAAE,GAAGS;QAAgB,CAAC,GAAGT,CAChE,CAAC,CAAC;QACFlE,OAAO,CAACmE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMgB,YAAY,GAAG;UACnB/D,EAAE,EAAEgE,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGV;QACL,CAAC;QACDpD,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE6D,YAAY,CAAC,CAAC;QAC5CnF,OAAO,CAACmE,OAAO,CAAC,MAAM,CAAC;MACzB;MACAxC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMkC,YAAY,GAAIpD,IAAI,IAAK;IAC7B,MAAMqD,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,MAAM,CAACrD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAM4D,cAAc,GAAI7C,MAAM,IAAK;IACjC,MAAMsC,MAAM,GAAG;MACbQ,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,QAAQ;MAClBL,OAAO,EAAE,KAAK;MACdM,MAAM,EAAE;IACV,CAAC;IACD,OAAOV,MAAM,CAACtC,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMiD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB3F,OAAA,CAACnB,KAAK;MAAA+G,QAAA,gBACJ5F,OAAA,CAACE,IAAI;QAAC2F,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BjG,OAAA,CAACf,GAAG;QAACiH,KAAK,EAAEzB,YAAY,CAACkB,MAAM,CAACtE,IAAI,CAAE;QAAAuE,QAAA,EACnCD,MAAM,CAACtE,IAAI,KAAK,UAAU,GAAG,IAAI,GACjCsE,MAAM,CAACtE,IAAI,KAAK,WAAW,GAAG,IAAI,GAClCsE,MAAM,CAACtE,IAAI,KAAK,WAAW,GAAG,IAAI,GAClCsE,MAAM,CAACtE,IAAI,KAAK,SAAS,GAAG,IAAI,GAChCsE,MAAM,CAACtE,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;MAAI;QAAAyE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGnE,KAAK,iBACZtB,OAAA,CAACnB,KAAK;MAAA+G,QAAA,gBACJ5F,OAAA,CAACL,YAAY;QAACwG,KAAK,EAAE;UAAED,KAAK,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CjG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAEtE;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACR;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/E,KAAK,GAAGgF,CAAC,CAAChF;EAChC,CAAC,EACD;IACEgE,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGlE,SAAS,iBAChBvB,OAAA,CAACV,IAAI;MAACiH,QAAQ;MAACC,KAAK,EAAEjF,SAAU;MAAC4E,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAG;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5D;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9E,SAAS,GAAG+E,CAAC,CAAC/E;EACpC,CAAC,EACD;IACE+D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGiB,IAAI,iBACX1G,OAAA,CAACnB,KAAK;MAAA+G,QAAA,gBACJ5F,OAAA,CAACF,mBAAmB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBjG,OAAA,CAACE,IAAI;QAAA0F,QAAA,EAAE,OAAOc,IAAI,KAAK,QAAQ,GAAG,GAAGA,IAAI,GAAG,GAAGA;MAAI;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGkB,QAAQ,iBACf3G,OAAA,CAACnB,KAAK;MAAA+G,QAAA,gBACJ5F,OAAA,CAACJ,mBAAmB;QAACuG,KAAK,EAAE;UAAED,KAAK,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDjG,OAAA,CAACV,IAAI;QAACiH,QAAQ;QAACC,KAAK,EAAEG,QAAS;QAACR,KAAK,EAAE;UAAEM,QAAQ,EAAE;QAAG;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3E,WAAW,GAAG4E,CAAC,CAAC5E;EACtC,CAAC,EACD;IACE4D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGnE,KAAK,IAAK;MACjB,MAAM4E,KAAK,GAAG5E,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;MAClG,oBAAOtB,OAAA,CAACf,GAAG;QAACiH,KAAK,EAAEA,KAAM;QAAAN,QAAA,GAAEtE,KAAK,EAAC,IAAE;MAAA;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3C,CAAC;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACnE,WAAW,GAAGoE,CAAC,CAACpE;EACtC,CAAC,EACD;IACEoD,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGrD,MAAM,iBACbpC,OAAA,CAACf,GAAG;MAACiH,KAAK,EAAEjB,cAAc,CAAC7C,MAAM,CAAE;MAAAwD,QAAA,EAChCxD,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,UAAU,GAAG,KAAK,GAC7BA,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;IAAI;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACmB,CAAC,EAAEjB,MAAM,kBAChB3F,OAAA,CAACnB,KAAK;MAAA+G,QAAA,gBACJ5F,OAAA,CAACZ,OAAO;QAACkG,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB5F,OAAA,CAACzB,MAAM;UACL8C,IAAI,EAAC,MAAM;UACXwF,IAAI,eAAE7G,OAAA,CAACR,YAAY;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAMpE,UAAU,CAACiD,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVjG,OAAA,CAACd,UAAU;QACToG,KAAK,EAAC,8DAAY;QAClByB,SAAS,EAAEA,CAAA,KAAM5D,YAAY,CAACwC,MAAM,CAACpF,EAAE,CAAE;QACzCyG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAArB,QAAA,eAEf5F,OAAA,CAACZ,OAAO;UAACkG,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjB5F,OAAA,CAACzB,MAAM;YACL8C,IAAI,EAAC,MAAM;YACX6F,MAAM;YACNL,IAAI,eAAE7G,OAAA,CAACP,cAAc;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEjG,OAAA;IAAKmH,SAAS,EAAC,SAAS;IAAAvB,QAAA,gBACtB5F,OAAA;MAAKmH,SAAS,EAAC,aAAa;MAAAvB,QAAA,gBAC1B5F,OAAA,CAACC,KAAK;QAACqB,KAAK,EAAE,CAAE;QAAC6F,SAAS,EAAC,YAAY;QAAAvB,QAAA,gBACrC5F,OAAA,CAACN,eAAe;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCACrB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjG,OAAA,CAACzB,MAAM;QACL8C,IAAI,EAAC,SAAS;QACdwF,IAAI,eAAE7G,OAAA,CAACT,YAAY;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAEtE,SAAU;QAAAoD,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjG,OAAA,CAACjB,GAAG;MAACqI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjB,KAAK,EAAE;QAAEkB,YAAY,EAAE;MAAG,CAAE;MAAAzB,QAAA,gBACjD5F,OAAA,CAAChB,GAAG;QAACsI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX5F,OAAA,CAAC3B,IAAI;UAACkJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB5F,OAAA,CAACX,SAAS;YACRiG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAE/F,UAAU,CAAC+G,MAAO;YACzBC,MAAM,eAAEzH,OAAA,CAACN,eAAe;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAAChB,GAAG;QAACsI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX5F,OAAA,CAAC3B,IAAI;UAACkJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB5F,OAAA,CAACX,SAAS;YACRiG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAE/F,UAAU,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,MAAM,KAAK,QAAQ,CAAC,CAACoF,MAAO;YAC5DC,MAAM,eAAEzH,OAAA,CAACL,YAAY;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzByB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAAChB,GAAG;QAACsI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX5F,OAAA,CAAC3B,IAAI;UAACkJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB5F,OAAA,CAACX,SAAS;YACRiG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAE/F,UAAU,CAAC2C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,MAAM,KAAK,SAAS,CAAC,CAACoF,MAAO;YAC7DC,MAAM,eAAEzH,OAAA,CAACJ,mBAAmB;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCyB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNjG,OAAA,CAAChB,GAAG;QAACsI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX5F,OAAA,CAAC3B,IAAI;UAACkJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB5F,OAAA,CAACX,SAAS;YACRiG,KAAK,EAAC,sCAAQ;YACdkB,KAAK,EAAE/F,UAAU,CAAC+G,MAAM,GAAG,CAAC,GAAG,CAAC/G,UAAU,CAACkH,MAAM,CAAC,CAACC,GAAG,EAAEvE,CAAC,KAAKuE,GAAG,GAAGvE,CAAC,CAACnB,WAAW,EAAE,CAAC,CAAC,GAAGzB,UAAU,CAAC+G,MAAM,EAAEK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE;YAC1HJ,MAAM,eAAEzH,OAAA,CAACH,WAAW;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxByB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjG,OAAA,CAAC3B,IAAI;MAAAuH,QAAA,eACH5F,OAAA,CAAC1B,KAAK;QACJ+G,OAAO,EAAEA,OAAQ;QACjByC,UAAU,EAAErH,UAAW;QACvBsH,MAAM,EAAC,IAAI;QACXpH,OAAO,EAAEA,OAAQ;QACjBqH,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPjG,OAAA,CAACxB,KAAK;MACJ8G,KAAK,EAAEvE,gBAAgB,GAAG,MAAM,GAAG,MAAO;MAC1CuH,IAAI,EAAEzH,YAAa;MACnB0H,QAAQ,EAAEA,CAAA,KAAMzH,eAAe,CAAC,KAAK,CAAE;MACvC0H,IAAI,EAAEA,CAAA,KAAMvH,IAAI,CAACwH,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAE/H,OAAQ;MACxBgI,KAAK,EAAE,GAAI;MAAA/C,QAAA,eAEX5F,OAAA,CAACvB,IAAI;QACHwC,IAAI,EAAEA,IAAK;QACX2H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEtF,YAAa;QACvBuF,aAAa,EAAE;UACbzH,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,CAAC;UACRC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,GAAG;UACbC,SAAS,EAAE,OAAO;UAClBC,WAAW,EAAE,CAAC;UACdC,aAAa,EAAE,CAAC;UAChBO,WAAW,EAAE,CAAC;UACdE,MAAM,EAAE;QACV,CAAE;QAAAwD,QAAA,gBAEF5F,OAAA,CAACjB,GAAG;UAACqI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd5F,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,MAAM;cACX4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACtB,KAAK;gBAACyK,WAAW,EAAC;cAAS;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,MAAM;cACX4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACrB,MAAM;gBAAAiH,QAAA,gBACL5F,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACjB,GAAG;UAACqI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd5F,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,OAAO;cACZ4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,EAAG;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,WAAW;cAChB4H,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAyG,QAAA,eAE/C5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,aAAa;cAClB4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACjB,GAAG;UAACqI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd5F,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,UAAU;cACf4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACE,IAAI,EAAE,GAAI;gBAACnD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO,CAAE;gBAACY,UAAU,EAAC;cAAG;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,aAAa;cAClB4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,eAAe;cACpB4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACpB,WAAW;gBAACwK,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACjB,GAAG;UAACqI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd5F,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,WAAW;cAChB4H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhD5F,OAAA,CAACrB,MAAM;gBAAAiH,QAAA,gBACL5F,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,MAAM;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,MAAM;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjG,OAAA,CAAChB,GAAG;YAACsI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ5F,OAAA,CAACvB,IAAI,CAACsK,IAAI;cACR3H,IAAI,EAAC,QAAQ;cACb4H,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE/J,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAyG,QAAA,eAE9C5F,OAAA,CAACrB,MAAM;gBAAAiH,QAAA,gBACL5F,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCjG,OAAA,CAACI,MAAM;kBAACoG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,cAAc;UAAC4H,KAAK,EAAC,oBAAK;UAAApD,QAAA,eACxC5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAQ;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,eAAe;UAAC4H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC7D5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAc;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,cAAc;UAAC4H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC5D5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAa;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,aAAa;UAAC4H,KAAK,EAAC,oBAAK;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC1D5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAc;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,gBAAgB;UAAC4H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC9D5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAkB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UAAC3H,IAAI,EAAC,qBAAqB;UAAC4H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eACnE5F,OAAA,CAACtB,KAAK;YAACyK,WAAW,EAAC;UAAY;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZjG,OAAA,CAACvB,IAAI,CAACsK,IAAI;UACR3H,IAAI,EAAC,aAAa;UAClB4H,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE/J,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAyG,QAAA,eAE9C5F,OAAA,CAACG,QAAQ;YAACsJ,IAAI,EAAE,CAAE;YAACN,WAAW,EAAC;UAAiB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAxiBID,kBAAkB;EAAA,QACIjC,SAAS,EAKpBK,IAAI,CAACyC,OAAO;AAAA;AAAAwI,EAAA,GANvBrJ,kBAAkB;AA0iBxB,eAAeA,kBAAkB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}